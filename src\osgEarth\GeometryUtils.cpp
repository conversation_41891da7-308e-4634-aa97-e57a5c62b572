/* -*-c++-*- */
/* osgEarth - Geospatial SDK for OpenSceneGraph
 * Copyright 2020 Pelican Mapping
 * http://osgearth.org
 *
 * osgEarth is free software; you can redistribute it and/or modify
 * it under the terms of the GNU Lesser General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 * FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS
 * IN THE SOFTWARE.
 *
 * You should have received a copy of the GNU Lesser General Public License
 * along with this program.  If not, see <http://www.gnu.org/licenses/>
 */

#include <osgEarth/GeometryUtils>
#include <osgEarth/GeosUtils>
#include <osgEarth/Notify>

using namespace osgEarth;

std::string
osgEarth::GeometryUtils::geometryToWKT(const Geometry *geometry)
{
    // Simplified WKT generation without GDAL/OGR
    if (!geometry)
        return "";

    std::string result;

    if (const Point *point = dynamic_cast<const Point *>(geometry))
    {
        if (point->size() > 0)
        {
            const osg::Vec3d &p = (*point)[0];
            result = "POINT(" + std::to_string(p.x()) + " " + std::to_string(p.y()) + ")";
        }
    }
    else if (const LineString *lineString = dynamic_cast<const LineString *>(geometry))
    {
        result = "LINESTRING(";
        for (size_t i = 0; i < lineString->size(); ++i)
        {
            if (i > 0)
                result += ",";
            const osg::Vec3d &p = (*lineString)[i];
            result += std::to_string(p.x()) + " " + std::to_string(p.y());
        }
        result += ")";
    }
    else if (const Polygon *polygon = dynamic_cast<const Polygon *>(geometry))
    {
        result = "POLYGON((";
        for (size_t i = 0; i < polygon->size(); ++i)
        {
            if (i > 0)
                result += ",";
            const osg::Vec3d &p = (*polygon)[i];
            result += std::to_string(p.x()) + " " + std::to_string(p.y());
        }
        result += "))";
    }
    else
    {
        OE_WARN << "[GeometryUtils] Unsupported geometry type for WKT conversion" << std::endl;
    }

    return result;
}

std::string
osgEarth::GeometryUtils::geometryToIsoWKT(const Geometry *geometry)
{
    // For now, ISO WKT is the same as regular WKT in our simplified implementation
    return geometryToWKT(geometry);
}

std::string
osgEarth::GeometryUtils::geometryToGeoJSON(const Geometry *geometry)
{
    // Simplified GeoJSON generation without GDAL/OGR
    if (!geometry)
        return "";

    std::string result;

    if (const Point *point = dynamic_cast<const Point *>(geometry))
    {
        if (point->size() > 0)
        {
            const osg::Vec3d &p = (*point)[0];
            result = "{\"type\":\"Point\",\"coordinates\":[" +
                     std::to_string(p.x()) + "," + std::to_string(p.y()) + "]}";
        }
    }
    else if (const LineString *lineString = dynamic_cast<const LineString *>(geometry))
    {
        result = "{\"type\":\"LineString\",\"coordinates\":[";
        for (size_t i = 0; i < lineString->size(); ++i)
        {
            if (i > 0)
                result += ",";
            const osg::Vec3d &p = (*lineString)[i];
            result += "[" + std::to_string(p.x()) + "," + std::to_string(p.y()) + "]";
        }
        result += "]}";
    }
    else if (const Polygon *polygon = dynamic_cast<const Polygon *>(geometry))
    {
        result = "{\"type\":\"Polygon\",\"coordinates\":[[";
        for (size_t i = 0; i < polygon->size(); ++i)
        {
            if (i > 0)
                result += ",";
            const osg::Vec3d &p = (*polygon)[i];
            result += "[" + std::to_string(p.x()) + "," + std::to_string(p.y()) + "]";
        }
        result += "]]}";
    }
    else
    {
        OE_WARN << "[GeometryUtils] Unsupported geometry type for GeoJSON conversion" << std::endl;
    }

    return result;
}

Geometry *
osgEarth::GeometryUtils::geometryFromGeoJSON(const std::string &geojson, bool rewindPolygons)
{
    // Simplified implementation - not supported without GDAL/OGR
    OE_WARN << "[GeometryUtils] geometryFromGeoJSON not supported in simplified build" << std::endl;
    return nullptr;
}

std::string
osgEarth::GeometryUtils::geometryToKML(const Geometry *geometry)
{
    // Simplified implementation - not supported without GDAL/OGR
    OE_WARN << "[GeometryUtils] geometryToKML not supported in simplified build" << std::endl;
    return "";
}

std::string
osgEarth::GeometryUtils::geometryToGML(const Geometry *geometry)
{
    // Simplified implementation - not supported without GDAL/OGR
    OE_WARN << "[GeometryUtils] geometryToGML not supported in simplified build" << std::endl;
    return "";
}

Geometry *
osgEarth::GeometryUtils::geometryFromWKT(const std::string &wkt, bool rewindPolygons)
{
    // Simplified implementation - not supported without GDAL/OGR
    OE_WARN << "[GeometryUtils] geometryFromWKT not supported in simplified build" << std::endl;
    return nullptr;
}

double
osgEarth::GeometryUtils::getGeometryArea(const Geometry *geometry)
{
    // Simplified implementation using basic polygon area calculation
    if (!geometry)
        return 0.0;

    if (const Polygon *polygon = dynamic_cast<const Polygon *>(geometry))
    {
        // Simple shoelace formula for polygon area
        double area = 0.0;
        size_t n = polygon->size();
        if (n < 3)
            return 0.0;

        for (size_t i = 0; i < n; ++i)
        {
            size_t j = (i + 1) % n;
            const osg::Vec3d &pi = (*polygon)[i];
            const osg::Vec3d &pj = (*polygon)[j];
            area += pi.x() * pj.y() - pj.x() * pi.y();
        }
        return std::abs(area) * 0.5;
    }

    return 0.0; // Other geometry types not supported
}
