osgEarth Simplified Earth Viewer
================================

Usage:
1. Double-click run_myviewer.bat to start the custom viewer
2. Or run from command line: bin\osgearth_myviewer.exe

Features:
- Display 3D Earth model
- Coordinate grid display  
- Real-time coordinate display
- Basic map operations (zoom, pan, rotate)

Controls:
- Left mouse drag: Rotate view
- Right mouse drag: Zoom
- Middle mouse drag: Pan
- 's' key: Show statistics
- 'w' key: Wireframe mode

Notes:
- If you have an earth.jpg texture file in the same directory, it will be used as Earth texture
- Move mouse over the Earth to see coordinate information in the console
- This is a simplified version using only OSG (OpenSceneGraph) without complex osgEarth dependencies

System Requirements:
- Windows 10/11
- OpenGL compatible graphics card
- Visual C++ Redistributable 2022

For help: bin\osgearth_myviewer.exe --help
