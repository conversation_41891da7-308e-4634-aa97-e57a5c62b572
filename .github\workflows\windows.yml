name: windows-x64-Release

on: [push, pull_request, workflow_dispatch]

env:
  VCPKG_BINARY_SOURCES : 'clear;x-gha,readwrite'
  # Customize the CMake build type here (Release, Debug, RelWithDebInfo, etc.)
  BUILD_TYPE: Release
  VCPKG_VERSION: 'master'

jobs:

  build:
    runs-on: ${{ matrix.os }}

    strategy:
      matrix:
        os: ['windows-2019']
        include:
          - os: 'windows-2019'
            triplet: 'x64-windows'
            mono: ''
            VCPKG_WORKSPACE: 'c:/vcpkg_own'

    steps:
    - uses: actions/checkout@v4
      with:
        submodules: recursive

    - name: Export GitHub Actions cache environment variables
      uses: actions/github-script@v7
      with:
        script: |
          core.exportVariable('ACTIONS_CACHE_URL', process.env.ACTIONS_CACHE_URL || '');
          core.exportVariable('ACTIONS_RUNTIME_TOKEN', process.env.ACTIONS_RUNTIME_TOKEN || '');

    - name: Installing vcpkg (windows)
      shell: 'bash'
      run: |
        cmake -E make_directory ${{ matrix.VCPKG_WORKSPACE }}
        cd ${{ matrix.VCPKG_WORKSPACE }}
        # git clone --depth 1 --branch ${{env.VCPKG_VERSION}} https://github.com/microsoft/vcpkg
        git clone https://github.com/microsoft/vcpkg
        cd vcpkg
        git checkout ${{env.VCPKG_VERSION}}
        cd ..
        ./vcpkg/bootstrap-vcpkg.bat -disableMetrics
        ${{ matrix.VCPKG_WORKSPACE }}/vcpkg/vcpkg version
            
    - name: Create Build Environment
      run: |
        cmake -E make_directory ${{runner.workspace}}/build

    - name: Download cesium-native
      shell: powershell
      run: |
        Invoke-WebRequest -Uri "https://pelican-public.s3.us-east-1.amazonaws.com/cesium-native-v0.37.0-vs2019.zip" -OutFile "C:\cesium-native.zip"
        Expand-Archive -Path "C:\cesium-native.zip" -DestinationPath "C:\cesium-native"

    - name: Configure CMake
      shell: bash
      working-directory: ${{ runner.workspace }}/build
      # Add the following for vcpkg install debugging: -DVCPKG_INSTALL_OPTIONS=--debug  
      run: |
        cmake $GITHUB_WORKSPACE -DWIN32_USE_MP=ON -DOSGEARTH_BUILD_LEGACY_SPLAT_NODEKIT=OFF -DOSGEARTH_BUILD_PROCEDURAL_NODEKIT=ON -DOSGEARTH_BUILD_CESIUM_NODEKIT=ON -DCESIUM_NATIVE_DIR=C:/cesium-native -DCMAKE_BUILD_TYPE=$BUILD_TYPE -DCMAKE_TOOLCHAIN_FILE=${{ matrix.VCPKG_WORKSPACE }}/vcpkg/scripts/buildsystems/vcpkg.cmake -DVCPKG_MANIFEST_DIR=$GITHUB_WORKSPACE

    - name: 'Upload cmake configure log artifact'
      uses: actions/upload-artifact@v4
      if: ${{ failure() }}
      with:
        name: cmake-log
        path: |
          ${{ runner.workspace }}/build/CMakeCache.txt
        retention-days: 1

    - name: Build
      working-directory: ${{ runner.workspace }}/build
      shell: bash
      run: cmake --build . --config $BUILD_TYPE
