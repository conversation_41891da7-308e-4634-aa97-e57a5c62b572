# osgEarth简化版技术架构设计

## 1. 项目概述

### 1.1 主要特点
- 移除了GDAL和PROJ库依赖，大幅简化了项目结构
- 保留了XYZ瓦片和AWS Terrarium高程数据源支持
- 提供基本的坐标系统转换功能
- 兼容原有的API接口，但功能有所限制

### 1.2 技术栈
- **核心库**: OpenSceneGraph (OSG)
- **网络库**: libcurl (通过HTTPClient)
- **几何库**: GEOS (用于基本几何操作)
- **图像处理**: OSG内置图像处理
- **坐标转换**: 自实现的ProjectionUtils

### 1.3 外部依赖
- OpenSceneGraph 3.6+
- GEOS 3.13+
- libcurl
- 移除了GDAL和PROJ依赖

## 2. 系统架构

### 2.1 整体架构图
```mermaid
graph TB
    A[应用层] --> B[osgEarth核心层]
    B --> C[数据源层]
    B --> D[渲染层]
    B --> E[坐标系统层]
    
    C --> F[XYZ瓦片源]
    C --> G[AWS Terrarium高程源]
    C --> H[简化要素源]
    
    D --> I[OSG渲染引擎]
    
    E --> J[简化SpatialReference]
    E --> K[ProjectionUtils]
    
    F --> L[HTTPClient]
    G --> L
```

### 2.2 数据流
```mermaid
sequenceDiagram
    participant App as 应用程序
    participant Map as Map
    participant Layer as ImageLayer
    participant XYZ as XYZ数据源
    participant HTTP as HTTPClient
    
    App->>Map: 添加XYZ图层
    Map->>Layer: 创建图层
    Layer->>XYZ: 请求瓦片
    XYZ->>HTTP: HTTP请求
    HTTP-->>XYZ: 返回图像数据
    XYZ-->>Layer: 返回GeoImage
    Layer-->>Map: 更新显示
    Map-->>App: 渲染完成
```

## 3. 主要组件详解

### 3.1 类图
```mermaid
classDiagram
    class SpatialReference {
        +create(string) SpatialReference*
        +transform(Vec3d, SpatialReference*) bool
        +isGeographic() bool
        +isProjected() bool
        +getGeographicSRS() SpatialReference*
    }
    
    class ProjectionUtils {
        +wgs84ToWebMercator(double, double) bool
        +webMercatorToWgs84(double, double) bool
        +geodeticToGeocentric(double, double, double) bool
    }
    
    class XYZImageLayer {
        +createImageImplementation(TileKey) GeoImage
        +getURL() URI
        +setURL(URI) void
    }
    
    class HTTPClient {
        +get(URI) HTTPResponse
        +globalInit() void
    }
    
    SpatialReference --> ProjectionUtils
    XYZImageLayer --> HTTPClient
```

### 3.2 简化SpatialReference类
- **功能**: 提供基本的坐标系统支持
- **支持的坐标系**: WGS84、Web Mercator、Geocentric
- **核心方法**:
  - `create()`: 创建坐标系统实例
  - `transform()`: 坐标转换
  - `isGeographic()`: 判断是否为地理坐标系

### 3.3 XYZ数据源类
- **功能**: 支持XYZ瓦片格式的地图数据
- **特点**: 
  - 支持谷歌地图、OpenStreetMap等标准XYZ服务
  - 自动处理瓦片缓存
  - 支持多线程下载

### 3.4 简化要素源类
- **功能**: 提供要素数据的存根实现
- **限制**: 不支持实际的要素数据读写
- **目的**: 保持API兼容性

## 4. 关键数据结构

### 4.1 主要存储结构
- **TileKey**: 瓦片索引键值
- **GeoImage**: 地理参考图像
- **GeoHeightField**: 地理参考高程场
- **Geometry**: 几何对象基类

### 4.2 内存数据结构
- **Vec3dVector**: 三维点集合
- **TileCache**: 瓦片缓存
- **Registry**: 全局注册表

### 4.3 网络数据结构
- **HTTPRequest**: HTTP请求对象
- **HTTPResponse**: HTTP响应对象
- **URI**: 统一资源标识符

## 5. 关键算法和流程

### 5.1 瓦片请求流程
```mermaid
flowchart TD
    A[请求瓦片] --> B[计算TileKey]
    B --> C[检查缓存]
    C --> D{缓存命中?}
    D -->|是| E[返回缓存数据]
    D -->|否| F[构造URL]
    F --> G[HTTP请求]
    G --> H[解析响应]
    H --> I[创建GeoImage]
    I --> J[存入缓存]
    J --> K[返回数据]
```

### 5.2 坐标转换流程
```mermaid
flowchart TD
    A[输入坐标] --> B[确定源坐标系]
    B --> C[确定目标坐标系]
    C --> D{需要转换?}
    D -->|否| E[直接返回]
    D -->|是| F[选择转换算法]
    F --> G[执行转换]
    G --> H[返回结果]
```

### 5.3 简化的坐标转换算法
- **WGS84 ↔ Web Mercator**: 使用标准墨卡托投影公式
- **Geographic ↔ Geocentric**: 使用椭球体参数进行转换
- **单位转换**: 度与米之间的近似转换

## 6. 并发控制

### 6.1 线程模型
- **主线程**: UI和渲染
- **HTTP线程池**: 并发下载瓦片
- **缓存线程**: 异步缓存管理

### 6.2 锁机制
- **SRS缓存锁**: 保护坐标系统缓存
- **瓦片缓存锁**: 保护瓦片缓存访问
- **HTTP客户端锁**: 保护网络请求

## 7. 容错和恢复机制

### 7.1 网络容错
- **重试机制**: 自动重试失败的HTTP请求
- **超时处理**: 设置合理的网络超时时间
- **降级策略**: 网络失败时使用缓存数据

### 7.2 数据完整性
- **缓存验证**: 验证缓存数据的有效性
- **错误日志**: 记录所有错误信息
- **优雅降级**: 功能不可用时提供替代方案

## 8. 性能优化

### 8.1 网络优化
- **并发下载**: 多线程并发请求瓦片
- **连接复用**: 复用HTTP连接
- **压缩传输**: 支持gzip压缩

### 8.2 内存优化
- **瓦片缓存**: LRU缓存策略
- **对象池**: 复用常用对象
- **延迟加载**: 按需加载数据

### 8.3 渲染优化
- **LOD控制**: 根据视距调整细节级别
- **视锥剔除**: 只渲染可见区域
- **纹理压缩**: 使用压缩纹理格式

## 9. 可扩展性考虑

### 9.1 数据源扩展
- **插件机制**: 支持新的数据源类型
- **配置驱动**: 通过配置文件添加数据源
- **标准接口**: 统一的数据源接口

### 9.2 坐标系统扩展
- **自定义坐标系**: 支持用户定义坐标系
- **转换参数**: 可配置的转换参数
- **精度控制**: 可调整的转换精度

## 10. 限制和约束

### 10.1 功能限制
- **不支持复杂的地理数据格式** (如Shapefile、GeoTIFF等)
- **不支持高精度的坐标转换**
- **不支持复杂的空间分析功能**
- **要素数据功能被禁用**

### 10.2 性能约束
- **网络依赖**: 需要稳定的网络连接
- **内存使用**: 缓存会占用较多内存
- **CPU使用**: 坐标转换会消耗CPU资源

## 11. 未来改进方向

### 11.1 功能增强
- **离线支持**: 支持离线地图数据
- **矢量瓦片**: 支持矢量瓦片格式
- **3D瓦片**: 支持3D Tiles标准

### 11.2 性能提升
- **GPU加速**: 利用GPU进行坐标转换
- **智能缓存**: 基于使用模式的智能缓存
- **预加载**: 预测性数据加载

### 11.3 易用性改进
- **配置工具**: 图形化配置工具
- **调试工具**: 更好的调试和诊断工具
- **文档完善**: 更详细的使用文档
