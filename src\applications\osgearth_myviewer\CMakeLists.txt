# 简化的地球查看器 - 只依赖OSG
set(TARGET_NAME osgearth_myviewer)

set(TARGET_SRC
    osgearth_myviewer.cpp
)

# 只依赖OSG库，不依赖osgEarth
find_package(OpenSceneGraph REQUIRED COMPONENTS osg osgUtil osgDB osgViewer osgGA)

add_executable(${TARGET_NAME} ${TARGET_SRC})

target_link_libraries(${TARGET_NAME}
    ${OPENSCENEGRAPH_LIBRARIES}
)

target_include_directories(${TARGET_NAME} PRIVATE
    ${OPENSCENEGRAPH_INCLUDE_DIRS}
)

# 设置编译属性
set_target_properties(${TARGET_NAME} PROPERTIES
    CXX_STANDARD 17
    CXX_STANDARD_REQUIRED ON
)

# 安装目标
install(TARGETS ${TARGET_NAME}
    RUNTIME DESTINATION bin
)
