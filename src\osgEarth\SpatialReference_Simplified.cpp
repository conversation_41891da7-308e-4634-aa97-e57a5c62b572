/* -*-c++-*- */
/* osgEarth - Geospatial SDK for OpenSceneGraph
 * Copyright 2025 Pelican Mapping
 * http://osgearth.org
 *
 * Simplified SpatialReference implementation without GDAL/PROJ dependencies
 */
#include <osgEarth/SpatialReference>
#include <osgEarth/Registry>
#include <osgEarth/Cube>
#include <osgEarth/LocalTangentPlane>
#include <osgEarth/Math>
#include <osgEarth/ProjectionUtils>
#include <osgEarth/Notify>
#include <osgEarth/StringUtils>
#include <osg/CoordinateSystemNode>
#include <map>
#include <mutex>

using namespace osgEarth;
using namespace osgEarth::Util;

#define LC "[SpatialReference] "

// Static cache for SRS instances
static std::map<SpatialReference::Key, osg::ref_ptr<SpatialReference>> s_srsCache;
static std::mutex s_srsCacheMutex;

// SpatialReference::Key implementation
SpatialReference::Key::Key() : 
    _horizInitType(INIT_UNKNOWN),
    _vertInitType(INIT_UNKNOWN)
{
}

SpatialReference::Key::Key(const std::string& horiz, const std::string& vert) :
    _horizInitString(horiz),
    _vertInitString(vert),
    _horizInitType(INIT_UNKNOWN),
    _vertInitType(INIT_UNKNOWN)
{
    // Determine init types based on string content
    if (!horiz.empty())
    {
        std::string lowerHoriz = Strings::toLower(horiz);
        if (lowerHoriz == "wgs84" || lowerHoriz == "epsg:4326")
            _horizInitType = INIT_WGS84;
        else if (lowerHoriz == "spherical-mercator" || lowerHoriz == "web-mercator" || 
                 lowerHoriz == "epsg:3857" || lowerHoriz == "epsg:900913")
            _horizInitType = INIT_SPHERICAL_MERCATOR;
        else if (lowerHoriz == "geocentric" || lowerHoriz == "ecef")
            _horizInitType = INIT_GEOCENTRIC;
        else
            _horizInitType = INIT_UNKNOWN;
    }
}

bool SpatialReference::Key::operator < (const Key& rhs) const
{
    if (_horizInitString < rhs._horizInitString) return true;
    if (_horizInitString > rhs._horizInitString) return false;
    return _vertInitString < rhs._vertInitString;
}

// SpatialReference implementation
SpatialReference::SpatialReference(void* handle, const std::string& type, const std::string& init_str, const std::string& name) :
    _handle(nullptr),
    _wkt(""),
    _proj4(""),
    _name(name),
    _init_type(init_str),
    _isGeographic(false),
    _isProjected(false),
    _isGeocentric(false),
    _isMercator(false),
    _isSphericalMercator(false),
    _isNorthPolar(false),
    _isSouthPolar(false),
    _isUserDefined(false),
    _isLTP(false),
    _ellipsoid(Ellipsoid::WGS84),
    _datum(VerticalDatum::NONE)
{
    // Initialize based on init string
    std::string lowerInit = Strings::toLower(init_str);
    
    if (lowerInit == "wgs84" || lowerInit == "epsg:4326")
    {
        setupWGS84();
    }
    else if (lowerInit == "spherical-mercator" || lowerInit == "web-mercator" || 
             lowerInit == "epsg:3857" || lowerInit == "epsg:900913")
    {
        setupWebMercator();
    }
    else if (lowerInit == "geocentric" || lowerInit == "ecef")
    {
        setupGeocentric();
    }
    else
    {
        OE_WARN << LC << "Unsupported SRS: " << init_str << std::endl;
    }
}

SpatialReference::~SpatialReference()
{
    // No cleanup needed for simplified implementation
}

void SpatialReference::setupWGS84()
{
    _name = "WGS84";
    _isGeographic = true;
    _wkt = "GEOGCS[\"WGS 84\",DATUM[\"WGS_1984\",SPHEROID[\"WGS 84\",6378137,298.257223563]],PRIMEM[\"Greenwich\",0],UNIT[\"degree\",0.0174532925199433]]";
    _proj4 = "+proj=longlat +datum=WGS84 +no_defs";
}

void SpatialReference::setupWebMercator()
{
    _name = "Web Mercator";
    _isProjected = true;
    _isMercator = true;
    _isSphericalMercator = true;
    _wkt = "PROJCS[\"WGS 84 / Pseudo-Mercator\",GEOGCS[\"WGS 84\",DATUM[\"WGS_1984\",SPHEROID[\"WGS 84\",6378137,298.257223563]],PRIMEM[\"Greenwich\",0],UNIT[\"degree\",0.0174532925199433]],PROJECTION[\"Mercator_1SP\"],PARAMETER[\"central_meridian\",0],PARAMETER[\"scale_factor\",1],PARAMETER[\"false_easting\",0],PARAMETER[\"false_northing\",0],UNIT[\"metre\",1]]";
    _proj4 = "+proj=merc +a=6378137 +b=6378137 +lat_ts=0.0 +lon_0=0.0 +x_0=0.0 +y_0=0 +k=1.0 +units=m +nadgrids=@null +wktext +no_defs";
}

void SpatialReference::setupGeocentric()
{
    _name = "Geocentric";
    _isGeocentric = true;
    _wkt = "GEOCCS[\"WGS 84\",DATUM[\"WGS_1984\",SPHEROID[\"WGS 84\",6378137,298.257223563]],PRIMEM[\"Greenwich\",0],UNIT[\"metre\",1]]";
    _proj4 = "+proj=geocent +datum=WGS84 +units=m +no_defs";
}

SpatialReference* SpatialReference::create(const std::string& init, const std::string& vdatum_init)
{
    Key key(init, vdatum_init);
    return createFromKey(key);
}

SpatialReference* SpatialReference::createFromKey(const Key& key)
{
    std::lock_guard<std::mutex> lock(s_srsCacheMutex);
    
    auto it = s_srsCache.find(key);
    if (it != s_srsCache.end())
    {
        return it->second.get();
    }
    
    SpatialReference* srs = new SpatialReference(nullptr, "", key._horizInitString, "");
    if (srs && srs->isValid())
    {
        s_srsCache[key] = srs;
        return srs;
    }
    
    delete srs;
    return nullptr;
}

bool SpatialReference::isValid() const
{
    return _isGeographic || _isProjected || _isGeocentric;
}

bool SpatialReference::isEquivalentTo(const SpatialReference* rhs) const
{
    if (!rhs) return false;
    
    // Simple comparison based on type
    if (_isGeographic && rhs->_isGeographic) return true;
    if (_isSphericalMercator && rhs->_isSphericalMercator) return true;
    if (_isGeocentric && rhs->_isGeocentric) return true;
    
    return false;
}

const SpatialReference* SpatialReference::getGeographicSRS() const
{
    if (_isGeographic) return this;
    return Registry::instance()->getSphericalMercatorProfile()->getSRS()->getGeographicSRS();
}

const SpatialReference* SpatialReference::getGeocentricSRS() const
{
    if (_isGeocentric) return this;
    return create("geocentric");
}

bool SpatialReference::transform(const osg::Vec3d& input, const SpatialReference* outputSRS, osg::Vec3d& output) const
{
    if (!outputSRS || isEquivalentTo(outputSRS))
    {
        output = input;
        return true;
    }
    
    // Use ProjectionUtils for basic transformations
    if (_isGeographic && outputSRS->_isSphericalMercator)
    {
        double x, y;
        if (ProjectionUtils::wgs84ToWebMercator(input.x(), input.y(), x, y))
        {
            output.set(x, y, input.z());
            return true;
        }
    }
    else if (_isSphericalMercator && outputSRS->_isGeographic)
    {
        double lon, lat;
        if (ProjectionUtils::webMercatorToWgs84(input.x(), input.y(), lon, lat))
        {
            output.set(lon, lat, input.z());
            return true;
        }
    }
    else if (_isGeographic && outputSRS->_isGeocentric)
    {
        double x, y, z;
        if (ProjectionUtils::geodeticToGeocentric(input.x(), input.y(), input.z(), x, y, z))
        {
            output.set(x, y, z);
            return true;
        }
    }
    else if (_isGeocentric && outputSRS->_isGeographic)
    {
        double lon, lat, alt;
        if (ProjectionUtils::geocentricToGeodetic(input.x(), input.y(), input.z(), lon, lat, alt))
        {
            output.set(lon, lat, alt);
            return true;
        }
    }
    
    OE_WARN << LC << "Unsupported coordinate transformation" << std::endl;
    return false;
}

bool SpatialReference::transform(std::vector<osg::Vec3d>& points, const SpatialReference* outputSRS) const
{
    if (!outputSRS) return false;
    
    for (auto& point : points)
    {
        osg::Vec3d output;
        if (!transform(point, outputSRS, output))
            return false;
        point = output;
    }
    
    return true;
}

double SpatialReference::transformUnits(double input, const SpatialReference* outputSRS, double fallback) const
{
    // Simplified unit transformation - assume meters for projected, degrees for geographic
    if (!outputSRS) return fallback;
    
    if (_isGeographic && outputSRS->_isProjected)
    {
        // Convert degrees to meters (approximate)
        return input * 111320.0; // meters per degree at equator
    }
    else if (_isProjected && outputSRS->_isGeographic)
    {
        // Convert meters to degrees (approximate)
        return input / 111320.0;
    }
    
    return input; // Same units
}

bool SpatialReference::populateCoordinateSystemNode(osg::CoordinateSystemNode* csn) const
{
    if (!csn) return false;
    
    csn->setCoordinateSystem(_wkt);
    csn->setEllipsoidModel(new osg::EllipsoidModel());
    
    return true;
}

const SpatialReference* SpatialReference::createUTMFromLonLat(const Angle& lon, const Angle& lat) const
{
    // Simplified UTM creation - not supported
    OE_WARN << LC << "UTM creation not supported in simplified build" << std::endl;
    return nullptr;
}
