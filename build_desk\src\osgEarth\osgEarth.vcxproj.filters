﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\AltitudeFilter.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\AltitudeSymbol.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\AnnotationData.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\AnnotationLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\AnnotationNode.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\AnnotationRegistry.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\AnnotationSettings.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\AnnotationUtils.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ArcGISServer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ArcGISTilePackage.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\AtlasBuilder.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\AttributesFilter.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\AutoClipPlaneHandler.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\AzureMaps.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\BboxDrawable.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\BBoxSymbol.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\BillboardResource.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\BillboardSymbol.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Bing.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Bounds.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\BufferFilter.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\BuildGeometryFilter.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\BuildTextFilter.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Cache.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CacheBin.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CachePolicy.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CacheSeed.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Callouts.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CameraUtils.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Capabilities.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CascadeDrapingDecorator.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CentroidFilter.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CesiumIon.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Chonk.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CircleNode.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ClampableNode.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ClampCallback.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Clamping.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ClampingTechnique.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ClipSpace.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ClusterNode.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Color.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ColorFilter.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Composite.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CompositeTiledModelLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Compressors.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CompressedArray.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Config.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ContourMap.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ConvertTypeFilter.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CoverageLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CoverageSymbol.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CropFilter.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CssUtils.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Cube.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CullingUtils.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\DateTime.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\DateTimeRange.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\DebugImageLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\DecalLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\DepthOffset.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Draggers.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\DrapeableNode.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\DrapingCullSet.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\DrapingTechnique.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\DrawInstanced.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\EarthManipulator.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ECEF.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Elevation.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ElevationLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ElevationLOD.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ElevationPool.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ElevationQuery.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ElevationRanges.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\EllipseNode.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Ellipsoid.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Ephemeris.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ExampleResources.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Expression.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Extension.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ExtrudeGeometryFilter.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ExtrusionSymbol.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FadeEffect.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Feature.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FeatureCursor.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FeatureDisplayLayout.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FeatureElevationLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FeatureImageLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FeatureModelGraph.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FeatureModelLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FeatureModelSource.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FeatureNode.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FeatureRasterizer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FeatureSDFLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FeatureSource.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FeatureSourceIndexNode.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FeatureStyleSorter.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FileGDBFeatureSource.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FileUtils.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Fill.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Filter.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FilterContext.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FilteredFeatureSource.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FlatteningLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FractalElevationLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FrameClock.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GARSGraticule.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GeoData.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GeodeticGraticule.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GeodeticLabelingEngine.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Geoid.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GeoMath.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Geometry.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GeometryClamper.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GeometryCloud.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GeometryCompiler.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GeometryFactory.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GeometryRasterizer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GeometryUtils.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GeoPositionNode.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GeoPositionNodeAutoScaler.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GEOS.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GeoTransform.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GLSLChunker.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GLUtils.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GraticuleLabelingEngine.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\HeightFieldUtils.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Horizon.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\HorizonClipPlane.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\HTM.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\HTTPClient.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\IconResource.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\IconSymbol.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ImageLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ImageMosaic.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ImageOverlay.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ImageOverlayEditor.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ImageToFeatureLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ImageToHeightFieldConverter.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ImageUtils.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\InstanceBuilder.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\InstanceCloud.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\InstanceResource.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\InstanceSymbol.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\IntersectionPicker.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\IOTypes.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\JoinLines.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\JsonUtils.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LabelNode.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LandCover.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LandCoverLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LatLongFormatter.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Layer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LayerShader.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Lighting.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LinearLineOfSight.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LineDrawable.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LineSymbol.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LocalGeometryNode.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LocalTangentPlane.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LODGenerator.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LogarithmicDepthBuffer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Map.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MapboxGLGlyphManager.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MapboxGLImageLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MapCallback.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MapNode.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MaterialLoader.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Math.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MBTiles.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MeasureTool.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MemCache.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MemoryUtils.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MeshConsolidator.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MeshFlattener.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MeshSubdivider.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MetadataNode.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MetaTile.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Metrics.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MGRSFormatter.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MGRSGraticule.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ModelLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ModelNode.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ModelResource.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ModelSource.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ModelSymbol.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MVT.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\NetworkMonitor.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\NodeUtils.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\NoiseTextureFactory.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Notify.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ObjectIDPicker.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ObjectIndex.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\OverlayDecorator.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\PagedNode.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\PatchLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\PBRMaterial.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\PhongLightingEffect.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\PlaceNode.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\PointDrawable.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\PointSymbol.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\PolygonizeLines.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\PolygonSymbol.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\PowerlineLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\PrimitiveIntersector.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Profile.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Progress.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Query.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\RadialLineOfSight.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Random.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\RectangleNode.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Registry.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\RenderSymbol.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ResampleFilter.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Resource.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ResourceCache.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ResourceLibrary.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Revisioning.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\RTTPicker.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ScaleFilter.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ScatterFilter.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\SceneGraphCallback.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ScreenSpaceLayout.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ScriptEngine.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ScriptFilter.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\SDF.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\SelectExtentTool.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Session.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ShaderFactory.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ShaderGenerator.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ShaderLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ShaderLoader.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ShaderMerger.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ShaderUtils.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Shadowing.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\SimpleOceanLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\SimplePager.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\SimplexNoise.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\SimplifyFilter.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Skins.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Sky.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\SkyView.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\SpatialReference_Simplified.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GDALImageLayer_Simplified.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\OGRFeatureSource_Simplified.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\OgrUtils_Simplified.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\StateSetCache.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Status.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\StringUtils.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Stroke.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Style.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\StyleSelector.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\StyleSheet.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\SubstituteModelFilter.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Symbol.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TDTiles.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Terrain.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TerrainConstraintLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TerrainEngineNode.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TerrainMeshLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TerrainOptions.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TerrainProfile.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TerrainResources.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TerrainTileModel.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TerrainTileModelFactory.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TessellateOperator.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Tessellator.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Text.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TextSymbol.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TextSymbolizer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TextureArena.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TextureBuffer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TextureBufferSerializer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TFS.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TFSPackager.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Threading.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ThreeDTilesLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TileCache.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TiledFeatureModelLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TiledModelLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TileEstimator.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TileHandler.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TileIndex.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TileIndexBuilder.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TileKey.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TileLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TileMesher.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TileRasterizer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TileSource.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TileSourceElevationLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TileSourceImageLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TileVisitor.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TimeControl.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TimeSeriesImage.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TMS.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TMSBackFiller.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TopologyGraph.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TrackNode.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TransformFilter.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Units.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\URI.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Utils.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\UTMGraticule.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\UTMLabelingEngine.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Version.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\VerticalDatum.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\VideoLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ViewFitter.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Viewpoint.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\VirtualProgram.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\VisibleLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\WFS.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\WindLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\WireLines.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\WMS.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\XmlUtils.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\XYZ.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\XYZFeatureSource.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\XYZModelLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\tinyxml\tinyxml.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\tinyxml\tinyxmlerror.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\tinyxml\tinystr.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\tinyxml\tinyxmlparser.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarth\AutoGenShaders.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarth\vector_tile.pb.cc">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarth\glyphs.pb.cc">
      <Filter>Sources</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\AGG.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\rtree.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\weemesh.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\weejobs.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\tinyxml\tinyxml.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\tinyxml\tinystr.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarth\vector_tile.pb.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarth\glyphs.pb.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\BuildConfig.in">
      <Filter>Templates</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Version.in">
      <Filter>Templates</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Shaders.cpp.in">
      <Filter>Templates</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\CMakeFiles\0afa7917d62789d6a5349f6f7a4d0c3d\vector_tile.pb.h.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\CMakeFiles\0afa7917d62789d6a5349f6f7a4d0c3d\glyphs.pb.h.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\CMakeFiles\0afa7917d62789d6a5349f6f7a4d0c3d\AutoGenShaders.cpp.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\AltitudeFilter">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\AltitudeSymbol">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\AnnotationData">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\AnnotationLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\AnnotationNode">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\AnnotationRegistry">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\AnnotationSettings">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\AnnotationUtils">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ArcGISServer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ArcGISTilePackage">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\AtlasBuilder">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\AttributesFilter">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\AutoClipPlaneHandler">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\AutoScaleCallback">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\AzureMaps">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\BboxDrawable">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\BBoxSymbol">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\BillboardResource">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\BillboardSymbol">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Bing">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Bounds">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\BufferFilter">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\BuildGeometryFilter">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\BuildTextFilter">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Cache">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CacheBin">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CachePolicy">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CacheSeed">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Callbacks">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Callouts">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CameraUtils">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Capabilities">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CascadeDrapingDecorator">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CentroidFilter">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CesiumIon">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Chonk">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CircleNode">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ClampableNode">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ClampCallback">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Clamping">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ClampingTechnique">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ClipSpace">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ClusterNode">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Color">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ColorFilter">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Common">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Composite">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CompressedArray">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CompositeTiledModelLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Config">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Containers">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ContourMap">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ConvertTypeFilter">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Coverage">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CoverageLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CoverageSymbol">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CropFilter">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CssUtils">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Cube">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CullingUtils">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\DateTime">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\DateTimeRange">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\DebugImageLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\DecalLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\DepthOffset">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Draggers">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\DrapeableNode">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\DrapingCullSet">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\DrapingTechnique">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\DrawInstanced">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\EarthManipulator">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ECEF">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Elevation">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ElevationLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ElevationLOD">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ElevationPool">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ElevationQuery">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ElevationRanges">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\EllipseNode">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Ellipsoid">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Endian">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Ephemeris">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ExampleResources">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Export">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Expression">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Extension">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ExtrudeGeometryFilter">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ExtrusionSymbol">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FadeEffect">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Feature">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FeatureCursor">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FeatureDisplayLayout">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FeatureElevationLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FeatureImageLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FeatureIndex">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FeatureModelGraph">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FeatureModelLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FeatureModelSource">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FeatureNode">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FeatureRasterizer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FeatureSDFLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FeatureSource">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FeatureSourceIndexNode">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FeatureStyleSorter">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FileUtils">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Fill">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Filter">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FilterContext">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FilteredFeatureSource">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FlatteningLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Formatter">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FractalElevationLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FrameClock">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GARSGraticule">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GeoCommon">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GeoData">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GeodeticGraticule">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GeodeticLabelingEngine">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Geoid">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GeoMath">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Geometry">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GeometryClamper">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GeometryCloud">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GeometryCompiler">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GeometryFactory">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GeometryRasterizer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GeometryUtils">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GeosUtils">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GeoPositionNode">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GeoPositionNodeAutoScaler">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GEOS">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GeoTransform">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GLSLChunker">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GLUtils">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GraticuleLabelingEngine">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\HeightFieldUtils">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Horizon">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\HorizonClipPlane">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\HTM">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\HTTPClient">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\IconResource">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\IconSymbol">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ImageLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ImageMosaic">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ImageOverlay">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ImageOverlayEditor">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ImageToFeatureLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ImageToHeightFieldConverter">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ImageUtils">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\InstanceBuilder">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\InstanceCloud">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\InstanceResource">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\InstanceSymbol">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\IntersectionPicker">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\IOTypes">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\JoinLines">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\JsonUtils">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LabelNode">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LandCover">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LandCoverLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LatLongFormatter">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Layer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LayerReference">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LayerShader">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Lighting">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LinearLineOfSight">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LineDrawable">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LineFunctor">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LineOfSight">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LineSymbol">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LoadableNode">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LocalGeometryNode">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LocalTangentPlane">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Locators">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LODGenerator">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LogarithmicDepthBuffer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Map">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MapboxGLGlyphManager">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MapboxGLImageLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MapCallback">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MapModelChange">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MapNode">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MapNodeObserver">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MaterialLoader">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Math">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MBTiles">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MeasureTool">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MemCache">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MemoryUtils">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MeshConsolidator">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MeshFlattener">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MeshSubdivider">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MetadataNode">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MetaTile">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Metrics">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MGRSFormatter">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MGRSGraticule">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ModelLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ModelNode">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ModelResource">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ModelSource">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ModelSymbol">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MVT">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\NativeProgramAdapter">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\NetworkMonitor">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\NodeUtils">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\NoiseTextureFactory">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Notify">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ObjectIDPicker">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ObjectIndex">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\optional">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\OverlayDecorator">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\PagedNode">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\PatchLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\PBRMaterial">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\PhongLightingEffect">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Picker">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\PlaceNode">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\PluginLoader">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\PointDrawable">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\PointSymbol">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\PolygonizeLines">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\PolygonSymbol">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\PowerlineLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\PrimitiveIntersector">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Profile">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Progress">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ProjectionUtils">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Query">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\RadialLineOfSight">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Random">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\RectangleNode">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\RefinePolicy">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Registry">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\RenderSymbol">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ResampleFilter">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Resource">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ResourceCache">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ResourceLibrary">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Revisioning">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\RTTPicker">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ScaleFilter">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ScatterFilter">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\SceneGraphCallback">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ScreenSpaceLayout">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ScreenSpaceLayoutCallout">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ScreenSpaceLayoutDeclutter">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ScreenSpaceLayoutImpl">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Script">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ScriptEngine">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ScriptFilter">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\SDF">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\SelectExtentTool">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Session">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ShaderFactory">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ShaderGenerator">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ShaderLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ShaderLoader">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ShaderMerger">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Shaders">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ShaderUtils">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Shadowing">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\SimpleOceanLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\SimplePager">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\SimplexNoise">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\SimplifyFilter">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\SimplifiedSpatialReference">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Skins">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Sky">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\SkyView">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\StarData">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\StateSetCache">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\StateTransition">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Status">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\StringUtils">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Stroke">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Style">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\StyleSelector">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\StyleSheet">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\SubstituteModelFilter">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Symbol">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Tags">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TDTiles">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Terrain">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TerrainConstraintLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TerrainEffect">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TerrainEngineNode">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TerrainEngineRequirements">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TerrainLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TerrainMeshLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TerrainOptions">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TerrainProfile">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TerrainResources">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TerrainTileModel">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TerrainTileModelFactory">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TerrainTileNode">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TessellateOperator">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Tessellator">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Text">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TextSymbol">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TextSymbolizer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TextureArena">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TextureBuffer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TFS">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TFSPackager">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Threading">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ThreeDTilesLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TileCache">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TiledFeatureModelLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TiledModelLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TileEstimator">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TileHandler">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TileIndex">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TileIndexBuilder">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TileKey">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TileLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TileMesher">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TileRasterizer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TileSource">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TileSourceElevationLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TileSourceImageLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TileVisitor">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TimeControl">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TimeSeriesImage">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TMS">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TMSBackFiller">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TopologyGraph">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TrackNode">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TransformFilter">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Units">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\URI">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Utils">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\UTMGraticule">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\UTMLabelingEngine">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\VerticalDatum">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\VideoLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ViewFitter">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Viewpoint">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\VirtualProgram">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\VisibleLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\WFS">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\WindLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\WireLines">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\WMS">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\XmlUtils">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\XYZ">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\XYZFeatureSource">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\XYZModelLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\build_include\osgEarth\Version">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\build_include\osgEarth\BuildConfig">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CascadeDraping.glsl">
      <Filter>Shaders</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Chonk.glsl">
      <Filter>Shaders</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Chonk.Culling.glsl">
      <Filter>Shaders</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\DepthOffset.glsl">
      <Filter>Shaders</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Draping.glsl">
      <Filter>Shaders</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\DrawInstancedAttribute.glsl">
      <Filter>Shaders</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GPUClamping.glsl">
      <Filter>Shaders</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GPUClamping.lib.glsl">
      <Filter>Shaders</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\HexTiling.glsl">
      <Filter>Shaders</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Instancing.glsl">
      <Filter>Shaders</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LineDrawable.glsl">
      <Filter>Shaders</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MetadataNode.glsl">
      <Filter>Shaders</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\WireLines.glsl">
      <Filter>Shaders</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\PhongLighting.glsl">
      <Filter>Shaders</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\PointDrawable.glsl">
      <Filter>Shaders</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Text.glsl">
      <Filter>Shaders</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Text_legacy.glsl">
      <Filter>Shaders</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ContourMap.glsl">
      <Filter>Shaders</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GeodeticGraticule.glsl">
      <Filter>Shaders</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LogDepthBuffer.glsl">
      <Filter>Shaders</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LogDepthBuffer.VertOnly.glsl">
      <Filter>Shaders</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ShadowCaster.glsl">
      <Filter>Shaders</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\SimpleOceanLayer.glsl">
      <Filter>Shaders</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\RTTPicker.glsl">
      <Filter>Shaders</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\WindLayer.CS.glsl">
      <Filter>Shaders</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\PBR.glsl">
      <Filter>Shaders</Filter>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="CMake Rules">
      <UniqueIdentifier>{D19B7673-F872-3132-A63A-C51407AE23E9}</UniqueIdentifier>
    </Filter>
    <Filter Include="Headers">
      <UniqueIdentifier>{EF867D52-4C69-3D13-B299-758B57B1F090}</UniqueIdentifier>
    </Filter>
    <Filter Include="Shaders">
      <UniqueIdentifier>{114E36F6-57CA-3F89-9153-A724FF8E371E}</UniqueIdentifier>
    </Filter>
    <Filter Include="Sources">
      <UniqueIdentifier>{C294197E-336F-3535-BB5B-294C331B48CF}</UniqueIdentifier>
    </Filter>
    <Filter Include="Templates">
      <UniqueIdentifier>{E7AA1402-8801-3E4F-9C60-6F7FAADABFCC}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
