// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: vector_tile.proto
// Protobuf C++ Version: 5.29.3

#include "vector_tile.pb.h"

#include <algorithm>
#include <type_traits>
#include "google/protobuf/io/coded_stream.h"
#include "google/protobuf/generated_message_tctable_impl.h"
#include "google/protobuf/extension_set.h"
#include "google/protobuf/generated_message_util.h"
#include "google/protobuf/wire_format_lite.h"
#include "google/protobuf/io/zero_copy_stream_impl_lite.h"
// @@protoc_insertion_point(includes)

// Must be included last.
#include "google/protobuf/port_def.inc"
PROTOBUF_PRAGMA_INIT_SEG
namespace _pb = ::google::protobuf;
namespace _pbi = ::google::protobuf::internal;
namespace _fl = ::google::protobuf::internal::field_layout;
namespace mapnik {
namespace vector {

inline constexpr tile_value::Impl_::Impl_(
    ::_pbi::ConstantInitialized) noexcept
      : _cached_size_{0},
        string_value_(
            &::google::protobuf::internal::fixed_address_empty_string,
            ::_pbi::ConstantInitialized()),
        double_value_{0},
        int_value_{::int64_t{0}},
        float_value_{0},
        bool_value_{false},
        uint_value_{::uint64_t{0u}},
        sint_value_{::int64_t{0}} {}

template <typename>
PROTOBUF_CONSTEXPR tile_value::tile_value(::_pbi::ConstantInitialized)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::MessageLite(_class_data_.base()),
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::MessageLite(),
#endif  // PROTOBUF_CUSTOM_VTABLE
      _impl_(::_pbi::ConstantInitialized()) {
}
struct tile_valueDefaultTypeInternal {
  PROTOBUF_CONSTEXPR tile_valueDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~tile_valueDefaultTypeInternal() {}
  union {
    tile_value _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 tile_valueDefaultTypeInternal _tile_value_default_instance_;

inline constexpr tile_feature::Impl_::Impl_(
    ::_pbi::ConstantInitialized) noexcept
      : _cached_size_{0},
        tags_{},
        _tags_cached_byte_size_{0},
        geometry_{},
        _geometry_cached_byte_size_{0},
        id_{::uint64_t{0u}},
        type_{static_cast< ::mapnik::vector::tile_GeomType >(0)} {}

template <typename>
PROTOBUF_CONSTEXPR tile_feature::tile_feature(::_pbi::ConstantInitialized)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::MessageLite(_class_data_.base()),
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::MessageLite(),
#endif  // PROTOBUF_CUSTOM_VTABLE
      _impl_(::_pbi::ConstantInitialized()) {
}
struct tile_featureDefaultTypeInternal {
  PROTOBUF_CONSTEXPR tile_featureDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~tile_featureDefaultTypeInternal() {}
  union {
    tile_feature _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 tile_featureDefaultTypeInternal _tile_feature_default_instance_;

inline constexpr tile_layer::Impl_::Impl_(
    ::_pbi::ConstantInitialized) noexcept
      : _cached_size_{0},
        features_{},
        keys_{},
        values_{},
        name_(
            &::google::protobuf::internal::fixed_address_empty_string,
            ::_pbi::ConstantInitialized()),
        extent_{4096u},
        version_{1u} {}

template <typename>
PROTOBUF_CONSTEXPR tile_layer::tile_layer(::_pbi::ConstantInitialized)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::MessageLite(_class_data_.base()),
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::MessageLite(),
#endif  // PROTOBUF_CUSTOM_VTABLE
      _impl_(::_pbi::ConstantInitialized()) {
}
struct tile_layerDefaultTypeInternal {
  PROTOBUF_CONSTEXPR tile_layerDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~tile_layerDefaultTypeInternal() {}
  union {
    tile_layer _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 tile_layerDefaultTypeInternal _tile_layer_default_instance_;

inline constexpr tile::Impl_::Impl_(
    ::_pbi::ConstantInitialized) noexcept
      : layers_{},
        _cached_size_{0} {}

template <typename>
PROTOBUF_CONSTEXPR tile::tile(::_pbi::ConstantInitialized)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::MessageLite(_class_data_.base()),
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::MessageLite(),
#endif  // PROTOBUF_CUSTOM_VTABLE
      _impl_(::_pbi::ConstantInitialized()) {
}
struct tileDefaultTypeInternal {
  PROTOBUF_CONSTEXPR tileDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~tileDefaultTypeInternal() {}
  union {
    tile _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 tileDefaultTypeInternal _tile_default_instance_;
}  // namespace vector
}  // namespace mapnik
namespace mapnik {
namespace vector {
PROTOBUF_CONSTINIT const uint32_t tile_GeomType_internal_data_[] = {
    262144u, 0u, };
bool tile_GeomType_IsValid(int value) {
  return 0 <= value && value <= 3;
}
static ::google::protobuf::internal::ExplicitlyConstructed<std::string>
    tile_GeomType_strings[4] = {};

static const char tile_GeomType_names[] = {
    "LineString"
    "Point"
    "Polygon"
    "Unknown"
};

static const ::google::protobuf::internal::EnumEntry tile_GeomType_entries[] =
    {
        {{&tile_GeomType_names[0], 10}, 2},
        {{&tile_GeomType_names[10], 5}, 1},
        {{&tile_GeomType_names[15], 7}, 3},
        {{&tile_GeomType_names[22], 7}, 0},
};

static const int tile_GeomType_entries_by_number[] = {
    3,  // 0 -> Unknown
    1,  // 1 -> Point
    0,  // 2 -> LineString
    2,  // 3 -> Polygon
};

const std::string& tile_GeomType_Name(tile_GeomType value) {
  static const bool kDummy =
      ::google::protobuf::internal::InitializeEnumStrings(
          tile_GeomType_entries, tile_GeomType_entries_by_number,
          4, tile_GeomType_strings);
  (void)kDummy;

  int idx = ::google::protobuf::internal::LookUpEnumName(
      tile_GeomType_entries, tile_GeomType_entries_by_number, 4,
      value);
  return idx == -1 ? ::google::protobuf::internal::GetEmptyString()
                   : tile_GeomType_strings[idx].get();
}

bool tile_GeomType_Parse(absl::string_view name, tile_GeomType* value) {
  int int_value;
  bool success = ::google::protobuf::internal::LookUpEnumValue(
      tile_GeomType_entries, 4, name, &int_value);
  if (success) {
    *value = static_cast<tile_GeomType>(int_value);
  }
  return success;
}
#if (__cplusplus < 201703) && \
  (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))

constexpr tile_GeomType tile::Unknown;
constexpr tile_GeomType tile::Point;
constexpr tile_GeomType tile::LineString;
constexpr tile_GeomType tile::Polygon;
constexpr tile_GeomType tile::GeomType_MIN;
constexpr tile_GeomType tile::GeomType_MAX;
constexpr int tile::GeomType_ARRAYSIZE;

#endif  // (__cplusplus < 201703) &&
        // (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))
// ===================================================================

class tile_value::_Internal {
 public:
  using HasBits =
      decltype(std::declval<tile_value>()._impl_._has_bits_);
  static constexpr ::int32_t kHasBitsOffset =
      8 * PROTOBUF_FIELD_OFFSET(tile_value, _impl_._has_bits_);
};

tile_value::tile_value(::google::protobuf::Arena* arena)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::MessageLite(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::MessageLite(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:mapnik.vector.tile.value)
}
inline PROTOBUF_NDEBUG_INLINE tile_value::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility, ::google::protobuf::Arena* arena,
    const Impl_& from, const ::mapnik::vector::tile_value& from_msg)
      : _extensions_{visibility, arena},
        _has_bits_{from._has_bits_},
        _cached_size_{0},
        string_value_(arena, from.string_value_) {}

tile_value::tile_value(
    ::google::protobuf::Arena* arena,
    const tile_value& from)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::MessageLite(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::MessageLite(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  tile_value* const _this = this;
  (void)_this;
  _internal_metadata_.MergeFrom<std::string>(
      from._internal_metadata_);
  new (&_impl_) Impl_(internal_visibility(), arena, from._impl_, from);
  _impl_._extensions_.MergeFrom(this, from._impl_._extensions_);
  ::memcpy(reinterpret_cast<char *>(&_impl_) +
               offsetof(Impl_, double_value_),
           reinterpret_cast<const char *>(&from._impl_) +
               offsetof(Impl_, double_value_),
           offsetof(Impl_, sint_value_) -
               offsetof(Impl_, double_value_) +
               sizeof(Impl_::sint_value_));

  // @@protoc_insertion_point(copy_constructor:mapnik.vector.tile.value)
}
inline PROTOBUF_NDEBUG_INLINE tile_value::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility,
    ::google::protobuf::Arena* arena)
      : _extensions_{visibility, arena},
        _cached_size_{0},
        string_value_(arena) {}

inline void tile_value::SharedCtor(::_pb::Arena* arena) {
  new (&_impl_) Impl_(internal_visibility(), arena);
  ::memset(reinterpret_cast<char *>(&_impl_) +
               offsetof(Impl_, double_value_),
           0,
           offsetof(Impl_, sint_value_) -
               offsetof(Impl_, double_value_) +
               sizeof(Impl_::sint_value_));
}
tile_value::~tile_value() {
  // @@protoc_insertion_point(destructor:mapnik.vector.tile.value)
  SharedDtor(*this);
}
inline void tile_value::SharedDtor(MessageLite& self) {
  tile_value& this_ = static_cast<tile_value&>(self);
  this_._internal_metadata_.Delete<std::string>();
  ABSL_DCHECK(this_.GetArena() == nullptr);
  this_._impl_.string_value_.Destroy();
  this_._impl_.~Impl_();
}

inline void* tile_value::PlacementNew_(const void*, void* mem,
                                        ::google::protobuf::Arena* arena) {
  return ::new (mem) tile_value(arena);
}
constexpr auto tile_value::InternalNewImpl_() {
  constexpr auto arena_bits = ::google::protobuf::internal::EncodePlacementArenaOffsets({
      PROTOBUF_FIELD_OFFSET(tile_value, _impl_._extensions_) +
          decltype(tile_value::_impl_._extensions_)::InternalGetArenaOffset(
              ::google::protobuf::MessageLite::internal_visibility()),
  });
  if (arena_bits.has_value()) {
    return ::google::protobuf::internal::MessageCreator::CopyInit(
        sizeof(tile_value), alignof(tile_value), *arena_bits);
  } else {
    return ::google::protobuf::internal::MessageCreator(&tile_value::PlacementNew_,
                                 sizeof(tile_value),
                                 alignof(tile_value));
  }
}
PROTOBUF_CONSTINIT
PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::google::protobuf::internal::ClassDataLite<25> tile_value::_class_data_ = {
    {
        &_tile_value_default_instance_._instance,
        &_table_.header,
        nullptr,  // OnDemandRegisterArenaDtor
        tile_value::IsInitializedImpl,
        &tile_value::MergeImpl,
        ::google::protobuf::MessageLite::GetNewImpl<tile_value>(),
#if defined(PROTOBUF_CUSTOM_VTABLE)
        &tile_value::SharedDtor,
        ::google::protobuf::MessageLite::GetClearImpl<tile_value>(), &tile_value::ByteSizeLong,
            &tile_value::_InternalSerialize,
#endif  // PROTOBUF_CUSTOM_VTABLE
        PROTOBUF_FIELD_OFFSET(tile_value, _impl_._cached_size_),
        true,
    },
    "mapnik.vector.tile.value",
};
const ::google::protobuf::internal::ClassData* tile_value::GetClassData() const {
  return _class_data_.base();
}
PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<3, 7, 0, 0, 2> tile_value::_table_ = {
  {
    PROTOBUF_FIELD_OFFSET(tile_value, _impl_._has_bits_),
    PROTOBUF_FIELD_OFFSET(tile_value, _impl_._extensions_),
    7, 56,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294967168,  // skipmap
    offsetof(decltype(_table_), field_entries),
    7,  // num_field_entries
    0,  // num_aux_entries
    offsetof(decltype(_table_), field_names),  // no aux_entries
    _class_data_.base(),
    nullptr,  // post_loop_handler
    ::_pbi::TcParser::GenericFallbackLite,  // fallback
    #ifdef PROTOBUF_PREFETCH_PARSE_TABLE
    ::_pbi::TcParser::GetTable<::mapnik::vector::tile_value>(),  // to_prefetch
    #endif  // PROTOBUF_PREFETCH_PARSE_TABLE
  }, {{
    {::_pbi::TcParser::MiniParse, {}},
    // optional string string_value = 1;
    {::_pbi::TcParser::FastBS1,
     {10, 0, 0, PROTOBUF_FIELD_OFFSET(tile_value, _impl_.string_value_)}},
    // optional float float_value = 2;
    {::_pbi::TcParser::FastF32S1,
     {21, 3, 0, PROTOBUF_FIELD_OFFSET(tile_value, _impl_.float_value_)}},
    // optional double double_value = 3;
    {::_pbi::TcParser::FastF64S1,
     {25, 1, 0, PROTOBUF_FIELD_OFFSET(tile_value, _impl_.double_value_)}},
    // optional int64 int_value = 4;
    {::_pbi::TcParser::FastV64S1,
     {32, 2, 0, PROTOBUF_FIELD_OFFSET(tile_value, _impl_.int_value_)}},
    // optional uint64 uint_value = 5;
    {::_pbi::TcParser::FastV64S1,
     {40, 5, 0, PROTOBUF_FIELD_OFFSET(tile_value, _impl_.uint_value_)}},
    // optional sint64 sint_value = 6;
    {::_pbi::TcParser::FastZ64S1,
     {48, 6, 0, PROTOBUF_FIELD_OFFSET(tile_value, _impl_.sint_value_)}},
    // optional bool bool_value = 7;
    {::_pbi::TcParser::FastV8S1,
     {56, 4, 0, PROTOBUF_FIELD_OFFSET(tile_value, _impl_.bool_value_)}},
  }}, {{
    65535, 65535
  }}, {{
    // optional string string_value = 1;
    {PROTOBUF_FIELD_OFFSET(tile_value, _impl_.string_value_), _Internal::kHasBitsOffset + 0, 0,
    (0 | ::_fl::kFcOptional | ::_fl::kBytes | ::_fl::kRepAString)},
    // optional float float_value = 2;
    {PROTOBUF_FIELD_OFFSET(tile_value, _impl_.float_value_), _Internal::kHasBitsOffset + 3, 0,
    (0 | ::_fl::kFcOptional | ::_fl::kFloat)},
    // optional double double_value = 3;
    {PROTOBUF_FIELD_OFFSET(tile_value, _impl_.double_value_), _Internal::kHasBitsOffset + 1, 0,
    (0 | ::_fl::kFcOptional | ::_fl::kDouble)},
    // optional int64 int_value = 4;
    {PROTOBUF_FIELD_OFFSET(tile_value, _impl_.int_value_), _Internal::kHasBitsOffset + 2, 0,
    (0 | ::_fl::kFcOptional | ::_fl::kInt64)},
    // optional uint64 uint_value = 5;
    {PROTOBUF_FIELD_OFFSET(tile_value, _impl_.uint_value_), _Internal::kHasBitsOffset + 5, 0,
    (0 | ::_fl::kFcOptional | ::_fl::kUInt64)},
    // optional sint64 sint_value = 6;
    {PROTOBUF_FIELD_OFFSET(tile_value, _impl_.sint_value_), _Internal::kHasBitsOffset + 6, 0,
    (0 | ::_fl::kFcOptional | ::_fl::kSInt64)},
    // optional bool bool_value = 7;
    {PROTOBUF_FIELD_OFFSET(tile_value, _impl_.bool_value_), _Internal::kHasBitsOffset + 4, 0,
    (0 | ::_fl::kFcOptional | ::_fl::kBool)},
  }},
  // no aux_entries
  {{
  }},
};

PROTOBUF_NOINLINE void tile_value::Clear() {
// @@protoc_insertion_point(message_clear_start:mapnik.vector.tile.value)
  ::google::protobuf::internal::TSanWrite(&_impl_);
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_._extensions_.Clear();
  cached_has_bits = _impl_._has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    _impl_.string_value_.ClearNonDefaultToEmpty();
  }
  if (cached_has_bits & 0x0000007eu) {
    ::memset(&_impl_.double_value_, 0, static_cast<::size_t>(
        reinterpret_cast<char*>(&_impl_.sint_value_) -
        reinterpret_cast<char*>(&_impl_.double_value_)) + sizeof(_impl_.sint_value_));
  }
  _impl_._has_bits_.Clear();
  _internal_metadata_.Clear<std::string>();
}

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::uint8_t* tile_value::_InternalSerialize(
            const MessageLite& base, ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) {
          const tile_value& this_ = static_cast<const tile_value&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::uint8_t* tile_value::_InternalSerialize(
            ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) const {
          const tile_value& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(serialize_to_array_start:mapnik.vector.tile.value)
          ::uint32_t cached_has_bits = 0;
          (void)cached_has_bits;

          cached_has_bits = this_._impl_._has_bits_[0];
          // optional string string_value = 1;
          if (cached_has_bits & 0x00000001u) {
            const std::string& _s = this_._internal_string_value();
            target = stream->WriteStringMaybeAliased(1, _s, target);
          }

          // optional float float_value = 2;
          if (cached_has_bits & 0x00000008u) {
            target = stream->EnsureSpace(target);
            target = ::_pbi::WireFormatLite::WriteFloatToArray(
                2, this_._internal_float_value(), target);
          }

          // optional double double_value = 3;
          if (cached_has_bits & 0x00000002u) {
            target = stream->EnsureSpace(target);
            target = ::_pbi::WireFormatLite::WriteDoubleToArray(
                3, this_._internal_double_value(), target);
          }

          // optional int64 int_value = 4;
          if (cached_has_bits & 0x00000004u) {
            target = ::google::protobuf::internal::WireFormatLite::
                WriteInt64ToArrayWithField<4>(
                    stream, this_._internal_int_value(), target);
          }

          // optional uint64 uint_value = 5;
          if (cached_has_bits & 0x00000020u) {
            target = stream->EnsureSpace(target);
            target = ::_pbi::WireFormatLite::WriteUInt64ToArray(
                5, this_._internal_uint_value(), target);
          }

          // optional sint64 sint_value = 6;
          if (cached_has_bits & 0x00000040u) {
            target = stream->EnsureSpace(target);
            target = ::_pbi::WireFormatLite::WriteSInt64ToArray(
                6, this_._internal_sint_value(), target);
          }

          // optional bool bool_value = 7;
          if (cached_has_bits & 0x00000010u) {
            target = stream->EnsureSpace(target);
            target = ::_pbi::WireFormatLite::WriteBoolToArray(
                7, this_._internal_bool_value(), target);
          }

          // Extension range [8, 536870912)
          target = this_._impl_._extensions_._InternalSerialize(
              internal_default_instance(), 8, 536870912, target, stream);
          if (PROTOBUF_PREDICT_FALSE(this_._internal_metadata_.have_unknown_fields())) {
            target = stream->WriteRaw(
                this_._internal_metadata_.unknown_fields<std::string>(::google::protobuf::internal::GetEmptyString).data(),
                static_cast<int>(this_._internal_metadata_.unknown_fields<std::string>(::google::protobuf::internal::GetEmptyString).size()), target);
          }
          // @@protoc_insertion_point(serialize_to_array_end:mapnik.vector.tile.value)
          return target;
        }

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::size_t tile_value::ByteSizeLong(const MessageLite& base) {
          const tile_value& this_ = static_cast<const tile_value&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::size_t tile_value::ByteSizeLong() const {
          const tile_value& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(message_byte_size_start:mapnik.vector.tile.value)
          ::size_t total_size = 0;
          total_size += this_._impl_._extensions_.ByteSize();

          ::uint32_t cached_has_bits = 0;
          // Prevent compiler warnings about cached_has_bits being unused
          (void)cached_has_bits;

          ::_pbi::Prefetch5LinesFrom7Lines(&this_);
          cached_has_bits = this_._impl_._has_bits_[0];
          if (cached_has_bits & 0x0000007fu) {
            // optional string string_value = 1;
            if (cached_has_bits & 0x00000001u) {
              total_size += 1 + ::google::protobuf::internal::WireFormatLite::StringSize(
                                              this_._internal_string_value());
            }
            // optional double double_value = 3;
            if (cached_has_bits & 0x00000002u) {
              total_size += 9;
            }
            // optional int64 int_value = 4;
            if (cached_has_bits & 0x00000004u) {
              total_size += ::_pbi::WireFormatLite::Int64SizePlusOne(
                  this_._internal_int_value());
            }
            // optional float float_value = 2;
            if (cached_has_bits & 0x00000008u) {
              total_size += 5;
            }
            // optional bool bool_value = 7;
            if (cached_has_bits & 0x00000010u) {
              total_size += 2;
            }
            // optional uint64 uint_value = 5;
            if (cached_has_bits & 0x00000020u) {
              total_size += ::_pbi::WireFormatLite::UInt64SizePlusOne(
                  this_._internal_uint_value());
            }
            // optional sint64 sint_value = 6;
            if (cached_has_bits & 0x00000040u) {
              total_size += ::_pbi::WireFormatLite::SInt64SizePlusOne(
                  this_._internal_sint_value());
            }
          }
          if (PROTOBUF_PREDICT_FALSE(this_._internal_metadata_.have_unknown_fields())) {
            total_size += this_._internal_metadata_.unknown_fields<std::string>(::google::protobuf::internal::GetEmptyString).size();
          }
          this_._impl_._cached_size_.Set(::_pbi::ToCachedSize(total_size));
          return total_size;
        }

void tile_value::MergeImpl(::google::protobuf::MessageLite& to_msg, const ::google::protobuf::MessageLite& from_msg) {
  auto* const _this = static_cast<tile_value*>(&to_msg);
  auto& from = static_cast<const tile_value&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:mapnik.vector.tile.value)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._impl_._has_bits_[0];
  if (cached_has_bits & 0x0000007fu) {
    if (cached_has_bits & 0x00000001u) {
      _this->_internal_set_string_value(from._internal_string_value());
    }
    if (cached_has_bits & 0x00000002u) {
      _this->_impl_.double_value_ = from._impl_.double_value_;
    }
    if (cached_has_bits & 0x00000004u) {
      _this->_impl_.int_value_ = from._impl_.int_value_;
    }
    if (cached_has_bits & 0x00000008u) {
      _this->_impl_.float_value_ = from._impl_.float_value_;
    }
    if (cached_has_bits & 0x00000010u) {
      _this->_impl_.bool_value_ = from._impl_.bool_value_;
    }
    if (cached_has_bits & 0x00000020u) {
      _this->_impl_.uint_value_ = from._impl_.uint_value_;
    }
    if (cached_has_bits & 0x00000040u) {
      _this->_impl_.sint_value_ = from._impl_.sint_value_;
    }
  }
  _this->_impl_._has_bits_[0] |= cached_has_bits;
  _this->_impl_._extensions_.MergeFrom(internal_default_instance(), from._impl_._extensions_);
  _this->_internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
}

void tile_value::CopyFrom(const tile_value& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:mapnik.vector.tile.value)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

PROTOBUF_NOINLINE bool tile_value::IsInitializedImpl(
    const MessageLite& msg) {
  auto& this_ = static_cast<const tile_value&>(msg);
  if (!this_._impl_._extensions_.IsInitialized(
          internal_default_instance())) {
    return false;
  }
  return true;
}

void tile_value::InternalSwap(tile_value* PROTOBUF_RESTRICT other) {
  using std::swap;
  _impl_._extensions_.InternalSwap(&other->_impl_._extensions_);
  auto* arena = GetArena();
  ABSL_DCHECK_EQ(arena, other->GetArena());
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_impl_._has_bits_[0], other->_impl_._has_bits_[0]);
  ::_pbi::ArenaStringPtr::InternalSwap(&_impl_.string_value_, &other->_impl_.string_value_, arena);
  ::google::protobuf::internal::memswap<
      PROTOBUF_FIELD_OFFSET(tile_value, _impl_.sint_value_)
      + sizeof(tile_value::_impl_.sint_value_)
      - PROTOBUF_FIELD_OFFSET(tile_value, _impl_.double_value_)>(
          reinterpret_cast<char*>(&_impl_.double_value_),
          reinterpret_cast<char*>(&other->_impl_.double_value_));
}

// ===================================================================

class tile_feature::_Internal {
 public:
  using HasBits =
      decltype(std::declval<tile_feature>()._impl_._has_bits_);
  static constexpr ::int32_t kHasBitsOffset =
      8 * PROTOBUF_FIELD_OFFSET(tile_feature, _impl_._has_bits_);
};

tile_feature::tile_feature(::google::protobuf::Arena* arena)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::MessageLite(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::MessageLite(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:mapnik.vector.tile.feature)
}
inline PROTOBUF_NDEBUG_INLINE tile_feature::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility, ::google::protobuf::Arena* arena,
    const Impl_& from, const ::mapnik::vector::tile_feature& from_msg)
      : _has_bits_{from._has_bits_},
        _cached_size_{0},
        tags_{visibility, arena, from.tags_},
        _tags_cached_byte_size_{0},
        geometry_{visibility, arena, from.geometry_},
        _geometry_cached_byte_size_{0} {}

tile_feature::tile_feature(
    ::google::protobuf::Arena* arena,
    const tile_feature& from)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::MessageLite(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::MessageLite(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  tile_feature* const _this = this;
  (void)_this;
  _internal_metadata_.MergeFrom<std::string>(
      from._internal_metadata_);
  new (&_impl_) Impl_(internal_visibility(), arena, from._impl_, from);
  ::memcpy(reinterpret_cast<char *>(&_impl_) +
               offsetof(Impl_, id_),
           reinterpret_cast<const char *>(&from._impl_) +
               offsetof(Impl_, id_),
           offsetof(Impl_, type_) -
               offsetof(Impl_, id_) +
               sizeof(Impl_::type_));

  // @@protoc_insertion_point(copy_constructor:mapnik.vector.tile.feature)
}
inline PROTOBUF_NDEBUG_INLINE tile_feature::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility,
    ::google::protobuf::Arena* arena)
      : _cached_size_{0},
        tags_{visibility, arena},
        _tags_cached_byte_size_{0},
        geometry_{visibility, arena},
        _geometry_cached_byte_size_{0} {}

inline void tile_feature::SharedCtor(::_pb::Arena* arena) {
  new (&_impl_) Impl_(internal_visibility(), arena);
  ::memset(reinterpret_cast<char *>(&_impl_) +
               offsetof(Impl_, id_),
           0,
           offsetof(Impl_, type_) -
               offsetof(Impl_, id_) +
               sizeof(Impl_::type_));
}
tile_feature::~tile_feature() {
  // @@protoc_insertion_point(destructor:mapnik.vector.tile.feature)
  SharedDtor(*this);
}
inline void tile_feature::SharedDtor(MessageLite& self) {
  tile_feature& this_ = static_cast<tile_feature&>(self);
  this_._internal_metadata_.Delete<std::string>();
  ABSL_DCHECK(this_.GetArena() == nullptr);
  this_._impl_.~Impl_();
}

inline void* tile_feature::PlacementNew_(const void*, void* mem,
                                        ::google::protobuf::Arena* arena) {
  return ::new (mem) tile_feature(arena);
}
constexpr auto tile_feature::InternalNewImpl_() {
  constexpr auto arena_bits = ::google::protobuf::internal::EncodePlacementArenaOffsets({
      PROTOBUF_FIELD_OFFSET(tile_feature, _impl_.tags_) +
          decltype(tile_feature::_impl_.tags_)::
              InternalGetArenaOffset(
                  ::google::protobuf::MessageLite::internal_visibility()),
      PROTOBUF_FIELD_OFFSET(tile_feature, _impl_.geometry_) +
          decltype(tile_feature::_impl_.geometry_)::
              InternalGetArenaOffset(
                  ::google::protobuf::MessageLite::internal_visibility()),
  });
  if (arena_bits.has_value()) {
    return ::google::protobuf::internal::MessageCreator::ZeroInit(
        sizeof(tile_feature), alignof(tile_feature), *arena_bits);
  } else {
    return ::google::protobuf::internal::MessageCreator(&tile_feature::PlacementNew_,
                                 sizeof(tile_feature),
                                 alignof(tile_feature));
  }
}
PROTOBUF_CONSTINIT
PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::google::protobuf::internal::ClassDataLite<27> tile_feature::_class_data_ = {
    {
        &_tile_feature_default_instance_._instance,
        &_table_.header,
        nullptr,  // OnDemandRegisterArenaDtor
        nullptr,  // IsInitialized
        &tile_feature::MergeImpl,
        ::google::protobuf::MessageLite::GetNewImpl<tile_feature>(),
#if defined(PROTOBUF_CUSTOM_VTABLE)
        &tile_feature::SharedDtor,
        ::google::protobuf::MessageLite::GetClearImpl<tile_feature>(), &tile_feature::ByteSizeLong,
            &tile_feature::_InternalSerialize,
#endif  // PROTOBUF_CUSTOM_VTABLE
        PROTOBUF_FIELD_OFFSET(tile_feature, _impl_._cached_size_),
        true,
    },
    "mapnik.vector.tile.feature",
};
const ::google::protobuf::internal::ClassData* tile_feature::GetClassData() const {
  return _class_data_.base();
}
PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<2, 4, 1, 0, 2> tile_feature::_table_ = {
  {
    PROTOBUF_FIELD_OFFSET(tile_feature, _impl_._has_bits_),
    0, // no _extensions_
    4, 24,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294967280,  // skipmap
    offsetof(decltype(_table_), field_entries),
    4,  // num_field_entries
    1,  // num_aux_entries
    offsetof(decltype(_table_), aux_entries),
    _class_data_.base(),
    nullptr,  // post_loop_handler
    ::_pbi::TcParser::GenericFallbackLite,  // fallback
    #ifdef PROTOBUF_PREFETCH_PARSE_TABLE
    ::_pbi::TcParser::GetTable<::mapnik::vector::tile_feature>(),  // to_prefetch
    #endif  // PROTOBUF_PREFETCH_PARSE_TABLE
  }, {{
    // repeated uint32 geometry = 4 [packed = true];
    {::_pbi::TcParser::FastV32P1,
     {34, 63, 0, PROTOBUF_FIELD_OFFSET(tile_feature, _impl_.geometry_)}},
    // optional uint64 id = 1;
    {::_pbi::TcParser::FastV64S1,
     {8, 0, 0, PROTOBUF_FIELD_OFFSET(tile_feature, _impl_.id_)}},
    // repeated uint32 tags = 2 [packed = true];
    {::_pbi::TcParser::FastV32P1,
     {18, 63, 0, PROTOBUF_FIELD_OFFSET(tile_feature, _impl_.tags_)}},
    // optional .mapnik.vector.tile.GeomType type = 3 [default = Unknown];
    {::_pbi::TcParser::FastEr0S1,
     {24, 1, 3, PROTOBUF_FIELD_OFFSET(tile_feature, _impl_.type_)}},
  }}, {{
    65535, 65535
  }}, {{
    // optional uint64 id = 1;
    {PROTOBUF_FIELD_OFFSET(tile_feature, _impl_.id_), _Internal::kHasBitsOffset + 0, 0,
    (0 | ::_fl::kFcOptional | ::_fl::kUInt64)},
    // repeated uint32 tags = 2 [packed = true];
    {PROTOBUF_FIELD_OFFSET(tile_feature, _impl_.tags_), -1, 0,
    (0 | ::_fl::kFcRepeated | ::_fl::kPackedUInt32)},
    // optional .mapnik.vector.tile.GeomType type = 3 [default = Unknown];
    {PROTOBUF_FIELD_OFFSET(tile_feature, _impl_.type_), _Internal::kHasBitsOffset + 1, 0,
    (0 | ::_fl::kFcOptional | ::_fl::kEnumRange)},
    // repeated uint32 geometry = 4 [packed = true];
    {PROTOBUF_FIELD_OFFSET(tile_feature, _impl_.geometry_), -1, 0,
    (0 | ::_fl::kFcRepeated | ::_fl::kPackedUInt32)},
  }}, {{
    {0, 4},
  }}, {{
  }},
};

PROTOBUF_NOINLINE void tile_feature::Clear() {
// @@protoc_insertion_point(message_clear_start:mapnik.vector.tile.feature)
  ::google::protobuf::internal::TSanWrite(&_impl_);
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.tags_.Clear();
  _impl_.geometry_.Clear();
  cached_has_bits = _impl_._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    ::memset(&_impl_.id_, 0, static_cast<::size_t>(
        reinterpret_cast<char*>(&_impl_.type_) -
        reinterpret_cast<char*>(&_impl_.id_)) + sizeof(_impl_.type_));
  }
  _impl_._has_bits_.Clear();
  _internal_metadata_.Clear<std::string>();
}

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::uint8_t* tile_feature::_InternalSerialize(
            const MessageLite& base, ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) {
          const tile_feature& this_ = static_cast<const tile_feature&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::uint8_t* tile_feature::_InternalSerialize(
            ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) const {
          const tile_feature& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(serialize_to_array_start:mapnik.vector.tile.feature)
          ::uint32_t cached_has_bits = 0;
          (void)cached_has_bits;

          cached_has_bits = this_._impl_._has_bits_[0];
          // optional uint64 id = 1;
          if (cached_has_bits & 0x00000001u) {
            target = stream->EnsureSpace(target);
            target = ::_pbi::WireFormatLite::WriteUInt64ToArray(
                1, this_._internal_id(), target);
          }

          // repeated uint32 tags = 2 [packed = true];
          {
            int byte_size = this_._impl_._tags_cached_byte_size_.Get();
            if (byte_size > 0) {
              target = stream->WriteUInt32Packed(
                  2, this_._internal_tags(), byte_size, target);
            }
          }

          // optional .mapnik.vector.tile.GeomType type = 3 [default = Unknown];
          if (cached_has_bits & 0x00000002u) {
            target = stream->EnsureSpace(target);
            target = ::_pbi::WireFormatLite::WriteEnumToArray(
                3, this_._internal_type(), target);
          }

          // repeated uint32 geometry = 4 [packed = true];
          {
            int byte_size = this_._impl_._geometry_cached_byte_size_.Get();
            if (byte_size > 0) {
              target = stream->WriteUInt32Packed(
                  4, this_._internal_geometry(), byte_size, target);
            }
          }

          if (PROTOBUF_PREDICT_FALSE(this_._internal_metadata_.have_unknown_fields())) {
            target = stream->WriteRaw(
                this_._internal_metadata_.unknown_fields<std::string>(::google::protobuf::internal::GetEmptyString).data(),
                static_cast<int>(this_._internal_metadata_.unknown_fields<std::string>(::google::protobuf::internal::GetEmptyString).size()), target);
          }
          // @@protoc_insertion_point(serialize_to_array_end:mapnik.vector.tile.feature)
          return target;
        }

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::size_t tile_feature::ByteSizeLong(const MessageLite& base) {
          const tile_feature& this_ = static_cast<const tile_feature&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::size_t tile_feature::ByteSizeLong() const {
          const tile_feature& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(message_byte_size_start:mapnik.vector.tile.feature)
          ::size_t total_size = 0;

          ::uint32_t cached_has_bits = 0;
          // Prevent compiler warnings about cached_has_bits being unused
          (void)cached_has_bits;

          ::_pbi::Prefetch5LinesFrom7Lines(&this_);
           {
            // repeated uint32 tags = 2 [packed = true];
            {
              total_size +=
                  ::_pbi::WireFormatLite::UInt32SizeWithPackedTagSize(
                      this_._internal_tags(), 1,
                      this_._impl_._tags_cached_byte_size_);
            }
            // repeated uint32 geometry = 4 [packed = true];
            {
              total_size +=
                  ::_pbi::WireFormatLite::UInt32SizeWithPackedTagSize(
                      this_._internal_geometry(), 1,
                      this_._impl_._geometry_cached_byte_size_);
            }
          }
          cached_has_bits = this_._impl_._has_bits_[0];
          if (cached_has_bits & 0x00000003u) {
            // optional uint64 id = 1;
            if (cached_has_bits & 0x00000001u) {
              total_size += ::_pbi::WireFormatLite::UInt64SizePlusOne(
                  this_._internal_id());
            }
            // optional .mapnik.vector.tile.GeomType type = 3 [default = Unknown];
            if (cached_has_bits & 0x00000002u) {
              total_size += 1 +
                            ::_pbi::WireFormatLite::EnumSize(this_._internal_type());
            }
          }
          if (PROTOBUF_PREDICT_FALSE(this_._internal_metadata_.have_unknown_fields())) {
            total_size += this_._internal_metadata_.unknown_fields<std::string>(::google::protobuf::internal::GetEmptyString).size();
          }
          this_._impl_._cached_size_.Set(::_pbi::ToCachedSize(total_size));
          return total_size;
        }

void tile_feature::MergeImpl(::google::protobuf::MessageLite& to_msg, const ::google::protobuf::MessageLite& from_msg) {
  auto* const _this = static_cast<tile_feature*>(&to_msg);
  auto& from = static_cast<const tile_feature&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:mapnik.vector.tile.feature)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_internal_mutable_tags()->MergeFrom(from._internal_tags());
  _this->_internal_mutable_geometry()->MergeFrom(from._internal_geometry());
  cached_has_bits = from._impl_._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      _this->_impl_.id_ = from._impl_.id_;
    }
    if (cached_has_bits & 0x00000002u) {
      _this->_impl_.type_ = from._impl_.type_;
    }
  }
  _this->_impl_._has_bits_[0] |= cached_has_bits;
  _this->_internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
}

void tile_feature::CopyFrom(const tile_feature& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:mapnik.vector.tile.feature)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}


void tile_feature::InternalSwap(tile_feature* PROTOBUF_RESTRICT other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_impl_._has_bits_[0], other->_impl_._has_bits_[0]);
  _impl_.tags_.InternalSwap(&other->_impl_.tags_);
  _impl_.geometry_.InternalSwap(&other->_impl_.geometry_);
  ::google::protobuf::internal::memswap<
      PROTOBUF_FIELD_OFFSET(tile_feature, _impl_.type_)
      + sizeof(tile_feature::_impl_.type_)
      - PROTOBUF_FIELD_OFFSET(tile_feature, _impl_.id_)>(
          reinterpret_cast<char*>(&_impl_.id_),
          reinterpret_cast<char*>(&other->_impl_.id_));
}

// ===================================================================

class tile_layer::_Internal {
 public:
  using HasBits =
      decltype(std::declval<tile_layer>()._impl_._has_bits_);
  static constexpr ::int32_t kHasBitsOffset =
      8 * PROTOBUF_FIELD_OFFSET(tile_layer, _impl_._has_bits_);
  static bool MissingRequiredFields(const HasBits& has_bits) {
    return ((has_bits[0] & 0x00000005) ^ 0x00000005) != 0;
  }
};

tile_layer::tile_layer(::google::protobuf::Arena* arena)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::MessageLite(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::MessageLite(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:mapnik.vector.tile.layer)
}
inline PROTOBUF_NDEBUG_INLINE tile_layer::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility, ::google::protobuf::Arena* arena,
    const Impl_& from, const ::mapnik::vector::tile_layer& from_msg)
      : _extensions_{visibility, arena},
        _has_bits_{from._has_bits_},
        _cached_size_{0},
        features_{visibility, arena, from.features_},
        keys_{visibility, arena, from.keys_},
        values_{visibility, arena, from.values_},
        name_(arena, from.name_) {}

tile_layer::tile_layer(
    ::google::protobuf::Arena* arena,
    const tile_layer& from)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::MessageLite(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::MessageLite(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  tile_layer* const _this = this;
  (void)_this;
  _internal_metadata_.MergeFrom<std::string>(
      from._internal_metadata_);
  new (&_impl_) Impl_(internal_visibility(), arena, from._impl_, from);
  _impl_._extensions_.MergeFrom(this, from._impl_._extensions_);
  ::memcpy(reinterpret_cast<char *>(&_impl_) +
               offsetof(Impl_, extent_),
           reinterpret_cast<const char *>(&from._impl_) +
               offsetof(Impl_, extent_),
           offsetof(Impl_, version_) -
               offsetof(Impl_, extent_) +
               sizeof(Impl_::version_));

  // @@protoc_insertion_point(copy_constructor:mapnik.vector.tile.layer)
}
inline PROTOBUF_NDEBUG_INLINE tile_layer::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility,
    ::google::protobuf::Arena* arena)
      : _extensions_{visibility, arena},
        _cached_size_{0},
        features_{visibility, arena},
        keys_{visibility, arena},
        values_{visibility, arena},
        name_(arena),
        extent_{4096u},
        version_{1u} {}

inline void tile_layer::SharedCtor(::_pb::Arena* arena) {
  new (&_impl_) Impl_(internal_visibility(), arena);
}
tile_layer::~tile_layer() {
  // @@protoc_insertion_point(destructor:mapnik.vector.tile.layer)
  SharedDtor(*this);
}
inline void tile_layer::SharedDtor(MessageLite& self) {
  tile_layer& this_ = static_cast<tile_layer&>(self);
  this_._internal_metadata_.Delete<std::string>();
  ABSL_DCHECK(this_.GetArena() == nullptr);
  this_._impl_.name_.Destroy();
  this_._impl_.~Impl_();
}

inline void* tile_layer::PlacementNew_(const void*, void* mem,
                                        ::google::protobuf::Arena* arena) {
  return ::new (mem) tile_layer(arena);
}
constexpr auto tile_layer::InternalNewImpl_() {
  constexpr auto arena_bits = ::google::protobuf::internal::EncodePlacementArenaOffsets({
      PROTOBUF_FIELD_OFFSET(tile_layer, _impl_._extensions_) +
          decltype(tile_layer::_impl_._extensions_)::InternalGetArenaOffset(
              ::google::protobuf::MessageLite::internal_visibility()),
      PROTOBUF_FIELD_OFFSET(tile_layer, _impl_.features_) +
          decltype(tile_layer::_impl_.features_)::
              InternalGetArenaOffset(
                  ::google::protobuf::MessageLite::internal_visibility()),
      PROTOBUF_FIELD_OFFSET(tile_layer, _impl_.keys_) +
          decltype(tile_layer::_impl_.keys_)::
              InternalGetArenaOffset(
                  ::google::protobuf::MessageLite::internal_visibility()),
      PROTOBUF_FIELD_OFFSET(tile_layer, _impl_.values_) +
          decltype(tile_layer::_impl_.values_)::
              InternalGetArenaOffset(
                  ::google::protobuf::MessageLite::internal_visibility()),
  });
  if (arena_bits.has_value()) {
    return ::google::protobuf::internal::MessageCreator::CopyInit(
        sizeof(tile_layer), alignof(tile_layer), *arena_bits);
  } else {
    return ::google::protobuf::internal::MessageCreator(&tile_layer::PlacementNew_,
                                 sizeof(tile_layer),
                                 alignof(tile_layer));
  }
}
PROTOBUF_CONSTINIT
PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::google::protobuf::internal::ClassDataLite<25> tile_layer::_class_data_ = {
    {
        &_tile_layer_default_instance_._instance,
        &_table_.header,
        nullptr,  // OnDemandRegisterArenaDtor
        tile_layer::IsInitializedImpl,
        &tile_layer::MergeImpl,
        ::google::protobuf::MessageLite::GetNewImpl<tile_layer>(),
#if defined(PROTOBUF_CUSTOM_VTABLE)
        &tile_layer::SharedDtor,
        ::google::protobuf::MessageLite::GetClearImpl<tile_layer>(), &tile_layer::ByteSizeLong,
            &tile_layer::_InternalSerialize,
#endif  // PROTOBUF_CUSTOM_VTABLE
        PROTOBUF_FIELD_OFFSET(tile_layer, _impl_._cached_size_),
        true,
    },
    "mapnik.vector.tile.layer",
};
const ::google::protobuf::internal::ClassData* tile_layer::GetClassData() const {
  return _class_data_.base();
}
PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<3, 6, 2, 0, 2> tile_layer::_table_ = {
  {
    PROTOBUF_FIELD_OFFSET(tile_layer, _impl_._has_bits_),
    PROTOBUF_FIELD_OFFSET(tile_layer, _impl_._extensions_),
    15, 56,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294950880,  // skipmap
    offsetof(decltype(_table_), field_entries),
    6,  // num_field_entries
    2,  // num_aux_entries
    offsetof(decltype(_table_), aux_entries),
    _class_data_.base(),
    nullptr,  // post_loop_handler
    ::_pbi::TcParser::GenericFallbackLite,  // fallback
    #ifdef PROTOBUF_PREFETCH_PARSE_TABLE
    ::_pbi::TcParser::GetTable<::mapnik::vector::tile_layer>(),  // to_prefetch
    #endif  // PROTOBUF_PREFETCH_PARSE_TABLE
  }, {{
    {::_pbi::TcParser::MiniParse, {}},
    // required string name = 1;
    {::_pbi::TcParser::FastBS1,
     {10, 0, 0, PROTOBUF_FIELD_OFFSET(tile_layer, _impl_.name_)}},
    // repeated .mapnik.vector.tile.feature features = 2;
    {::_pbi::TcParser::FastMtR1,
     {18, 63, 0, PROTOBUF_FIELD_OFFSET(tile_layer, _impl_.features_)}},
    // repeated string keys = 3;
    {::_pbi::TcParser::FastBR1,
     {26, 63, 0, PROTOBUF_FIELD_OFFSET(tile_layer, _impl_.keys_)}},
    // repeated .mapnik.vector.tile.value values = 4;
    {::_pbi::TcParser::FastMtR1,
     {34, 63, 1, PROTOBUF_FIELD_OFFSET(tile_layer, _impl_.values_)}},
    // optional uint32 extent = 5 [default = 4096];
    {::_pbi::TcParser::FastV32S1,
     {40, 1, 0, PROTOBUF_FIELD_OFFSET(tile_layer, _impl_.extent_)}},
    {::_pbi::TcParser::MiniParse, {}},
    // required uint32 version = 15 [default = 1];
    {::_pbi::TcParser::FastV32S1,
     {120, 2, 0, PROTOBUF_FIELD_OFFSET(tile_layer, _impl_.version_)}},
  }}, {{
    65535, 65535
  }}, {{
    // required string name = 1;
    {PROTOBUF_FIELD_OFFSET(tile_layer, _impl_.name_), _Internal::kHasBitsOffset + 0, 0,
    (0 | ::_fl::kFcOptional | ::_fl::kBytes | ::_fl::kRepAString)},
    // repeated .mapnik.vector.tile.feature features = 2;
    {PROTOBUF_FIELD_OFFSET(tile_layer, _impl_.features_), -1, 0,
    (0 | ::_fl::kFcRepeated | ::_fl::kMessage | ::_fl::kTvTable)},
    // repeated string keys = 3;
    {PROTOBUF_FIELD_OFFSET(tile_layer, _impl_.keys_), -1, 0,
    (0 | ::_fl::kFcRepeated | ::_fl::kBytes | ::_fl::kRepSString)},
    // repeated .mapnik.vector.tile.value values = 4;
    {PROTOBUF_FIELD_OFFSET(tile_layer, _impl_.values_), -1, 1,
    (0 | ::_fl::kFcRepeated | ::_fl::kMessage | ::_fl::kTvTable)},
    // optional uint32 extent = 5 [default = 4096];
    {PROTOBUF_FIELD_OFFSET(tile_layer, _impl_.extent_), _Internal::kHasBitsOffset + 1, 0,
    (0 | ::_fl::kFcOptional | ::_fl::kUInt32)},
    // required uint32 version = 15 [default = 1];
    {PROTOBUF_FIELD_OFFSET(tile_layer, _impl_.version_), _Internal::kHasBitsOffset + 2, 0,
    (0 | ::_fl::kFcOptional | ::_fl::kUInt32)},
  }}, {{
    {::_pbi::TcParser::GetTable<::mapnik::vector::tile_feature>()},
    {::_pbi::TcParser::GetTable<::mapnik::vector::tile_value>()},
  }}, {{
  }},
};

PROTOBUF_NOINLINE void tile_layer::Clear() {
// @@protoc_insertion_point(message_clear_start:mapnik.vector.tile.layer)
  ::google::protobuf::internal::TSanWrite(&_impl_);
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_._extensions_.Clear();
  _impl_.features_.Clear();
  _impl_.keys_.Clear();
  _impl_.values_.Clear();
  cached_has_bits = _impl_._has_bits_[0];
  if (cached_has_bits & 0x00000007u) {
    if (cached_has_bits & 0x00000001u) {
      _impl_.name_.ClearNonDefaultToEmpty();
    }
    _impl_.extent_ = 4096u;
    _impl_.version_ = 1u;
  }
  _impl_._has_bits_.Clear();
  _internal_metadata_.Clear<std::string>();
}

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::uint8_t* tile_layer::_InternalSerialize(
            const MessageLite& base, ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) {
          const tile_layer& this_ = static_cast<const tile_layer&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::uint8_t* tile_layer::_InternalSerialize(
            ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) const {
          const tile_layer& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(serialize_to_array_start:mapnik.vector.tile.layer)
          ::uint32_t cached_has_bits = 0;
          (void)cached_has_bits;

          cached_has_bits = this_._impl_._has_bits_[0];
          // required string name = 1;
          if (cached_has_bits & 0x00000001u) {
            const std::string& _s = this_._internal_name();
            target = stream->WriteStringMaybeAliased(1, _s, target);
          }

          // repeated .mapnik.vector.tile.feature features = 2;
          for (unsigned i = 0, n = static_cast<unsigned>(
                                   this_._internal_features_size());
               i < n; i++) {
            const auto& repfield = this_._internal_features().Get(i);
            target =
                ::google::protobuf::internal::WireFormatLite::InternalWriteMessage(
                    2, repfield, repfield.GetCachedSize(),
                    target, stream);
          }

          // repeated string keys = 3;
          for (int i = 0, n = this_._internal_keys_size(); i < n; ++i) {
            const auto& s = this_._internal_keys().Get(i);
            target = stream->WriteString(3, s, target);
          }

          // repeated .mapnik.vector.tile.value values = 4;
          for (unsigned i = 0, n = static_cast<unsigned>(
                                   this_._internal_values_size());
               i < n; i++) {
            const auto& repfield = this_._internal_values().Get(i);
            target =
                ::google::protobuf::internal::WireFormatLite::InternalWriteMessage(
                    4, repfield, repfield.GetCachedSize(),
                    target, stream);
          }

          // optional uint32 extent = 5 [default = 4096];
          if (cached_has_bits & 0x00000002u) {
            target = stream->EnsureSpace(target);
            target = ::_pbi::WireFormatLite::WriteUInt32ToArray(
                5, this_._internal_extent(), target);
          }

          // required uint32 version = 15 [default = 1];
          if (cached_has_bits & 0x00000004u) {
            target = stream->EnsureSpace(target);
            target = ::_pbi::WireFormatLite::WriteUInt32ToArray(
                15, this_._internal_version(), target);
          }

          // Extension range [16, 536870912)
          target = this_._impl_._extensions_._InternalSerialize(
              internal_default_instance(), 16, 536870912, target, stream);
          if (PROTOBUF_PREDICT_FALSE(this_._internal_metadata_.have_unknown_fields())) {
            target = stream->WriteRaw(
                this_._internal_metadata_.unknown_fields<std::string>(::google::protobuf::internal::GetEmptyString).data(),
                static_cast<int>(this_._internal_metadata_.unknown_fields<std::string>(::google::protobuf::internal::GetEmptyString).size()), target);
          }
          // @@protoc_insertion_point(serialize_to_array_end:mapnik.vector.tile.layer)
          return target;
        }

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::size_t tile_layer::ByteSizeLong(const MessageLite& base) {
          const tile_layer& this_ = static_cast<const tile_layer&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::size_t tile_layer::ByteSizeLong() const {
          const tile_layer& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(message_byte_size_start:mapnik.vector.tile.layer)
          ::size_t total_size = 0;
          total_size += this_._impl_._extensions_.ByteSize();

          ::uint32_t cached_has_bits = 0;
          // Prevent compiler warnings about cached_has_bits being unused
          (void)cached_has_bits;

          ::_pbi::Prefetch5LinesFrom7Lines(&this_);
           {
            // repeated .mapnik.vector.tile.feature features = 2;
            {
              total_size += 1UL * this_._internal_features_size();
              for (const auto& msg : this_._internal_features()) {
                total_size += ::google::protobuf::internal::WireFormatLite::MessageSize(msg);
              }
            }
            // repeated string keys = 3;
            {
              total_size +=
                  1 * ::google::protobuf::internal::FromIntSize(this_._internal_keys().size());
              for (int i = 0, n = this_._internal_keys().size(); i < n; ++i) {
                total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
                    this_._internal_keys().Get(i));
              }
            }
            // repeated .mapnik.vector.tile.value values = 4;
            {
              total_size += 1UL * this_._internal_values_size();
              for (const auto& msg : this_._internal_values()) {
                total_size += ::google::protobuf::internal::WireFormatLite::MessageSize(msg);
              }
            }
          }
           {
            // required string name = 1;
            cached_has_bits = this_._impl_._has_bits_[0];
            if (cached_has_bits & 0x00000001u) {
              total_size += 1 + ::google::protobuf::internal::WireFormatLite::StringSize(
                                              this_._internal_name());
            }
          }
           {
            // optional uint32 extent = 5 [default = 4096];
            if (cached_has_bits & 0x00000002u) {
              total_size += ::_pbi::WireFormatLite::UInt32SizePlusOne(
                  this_._internal_extent());
            }
          }
           {
            // required uint32 version = 15 [default = 1];
            if (cached_has_bits & 0x00000004u) {
              total_size += ::_pbi::WireFormatLite::UInt32SizePlusOne(
                  this_._internal_version());
            }
          }
          if (PROTOBUF_PREDICT_FALSE(this_._internal_metadata_.have_unknown_fields())) {
            total_size += this_._internal_metadata_.unknown_fields<std::string>(::google::protobuf::internal::GetEmptyString).size();
          }
          this_._impl_._cached_size_.Set(::_pbi::ToCachedSize(total_size));
          return total_size;
        }

void tile_layer::MergeImpl(::google::protobuf::MessageLite& to_msg, const ::google::protobuf::MessageLite& from_msg) {
  auto* const _this = static_cast<tile_layer*>(&to_msg);
  auto& from = static_cast<const tile_layer&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:mapnik.vector.tile.layer)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_internal_mutable_features()->MergeFrom(
      from._internal_features());
  _this->_internal_mutable_keys()->MergeFrom(from._internal_keys());
  _this->_internal_mutable_values()->MergeFrom(
      from._internal_values());
  cached_has_bits = from._impl_._has_bits_[0];
  if (cached_has_bits & 0x00000007u) {
    if (cached_has_bits & 0x00000001u) {
      _this->_internal_set_name(from._internal_name());
    }
    if (cached_has_bits & 0x00000002u) {
      _this->_impl_.extent_ = from._impl_.extent_;
    }
    if (cached_has_bits & 0x00000004u) {
      _this->_impl_.version_ = from._impl_.version_;
    }
  }
  _this->_impl_._has_bits_[0] |= cached_has_bits;
  _this->_impl_._extensions_.MergeFrom(internal_default_instance(), from._impl_._extensions_);
  _this->_internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
}

void tile_layer::CopyFrom(const tile_layer& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:mapnik.vector.tile.layer)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

PROTOBUF_NOINLINE bool tile_layer::IsInitializedImpl(
    const MessageLite& msg) {
  auto& this_ = static_cast<const tile_layer&>(msg);
  if (!this_._impl_._extensions_.IsInitialized(
          internal_default_instance())) {
    return false;
  }
  if (_Internal::MissingRequiredFields(this_._impl_._has_bits_)) {
    return false;
  }
  if (!::google::protobuf::internal::AllAreInitialized(this_._internal_values()))
    return false;
  return true;
}

void tile_layer::InternalSwap(tile_layer* PROTOBUF_RESTRICT other) {
  using std::swap;
  _impl_._extensions_.InternalSwap(&other->_impl_._extensions_);
  auto* arena = GetArena();
  ABSL_DCHECK_EQ(arena, other->GetArena());
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_impl_._has_bits_[0], other->_impl_._has_bits_[0]);
  _impl_.features_.InternalSwap(&other->_impl_.features_);
  _impl_.keys_.InternalSwap(&other->_impl_.keys_);
  _impl_.values_.InternalSwap(&other->_impl_.values_);
  ::_pbi::ArenaStringPtr::InternalSwap(&_impl_.name_, &other->_impl_.name_, arena);
  ::google::protobuf::internal::memswap<
      PROTOBUF_FIELD_OFFSET(tile_layer, _impl_.version_)
      + sizeof(tile_layer::_impl_.version_)
      - PROTOBUF_FIELD_OFFSET(tile_layer, _impl_.extent_)>(
          reinterpret_cast<char*>(&_impl_.extent_),
          reinterpret_cast<char*>(&other->_impl_.extent_));
}

// ===================================================================

class tile::_Internal {
 public:
};

tile::tile(::google::protobuf::Arena* arena)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::MessageLite(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::MessageLite(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:mapnik.vector.tile)
}
inline PROTOBUF_NDEBUG_INLINE tile::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility, ::google::protobuf::Arena* arena,
    const Impl_& from, const ::mapnik::vector::tile& from_msg)
      : _extensions_{visibility, arena},
        layers_{visibility, arena, from.layers_},
        _cached_size_{0} {}

tile::tile(
    ::google::protobuf::Arena* arena,
    const tile& from)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::MessageLite(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::MessageLite(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  tile* const _this = this;
  (void)_this;
  _internal_metadata_.MergeFrom<std::string>(
      from._internal_metadata_);
  new (&_impl_) Impl_(internal_visibility(), arena, from._impl_, from);
  _impl_._extensions_.MergeFrom(this, from._impl_._extensions_);

  // @@protoc_insertion_point(copy_constructor:mapnik.vector.tile)
}
inline PROTOBUF_NDEBUG_INLINE tile::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility,
    ::google::protobuf::Arena* arena)
      : _extensions_{visibility, arena},
        layers_{visibility, arena},
        _cached_size_{0} {}

inline void tile::SharedCtor(::_pb::Arena* arena) {
  new (&_impl_) Impl_(internal_visibility(), arena);
}
tile::~tile() {
  // @@protoc_insertion_point(destructor:mapnik.vector.tile)
  SharedDtor(*this);
}
inline void tile::SharedDtor(MessageLite& self) {
  tile& this_ = static_cast<tile&>(self);
  this_._internal_metadata_.Delete<std::string>();
  ABSL_DCHECK(this_.GetArena() == nullptr);
  this_._impl_.~Impl_();
}

inline void* tile::PlacementNew_(const void*, void* mem,
                                        ::google::protobuf::Arena* arena) {
  return ::new (mem) tile(arena);
}
constexpr auto tile::InternalNewImpl_() {
  constexpr auto arena_bits = ::google::protobuf::internal::EncodePlacementArenaOffsets({
      PROTOBUF_FIELD_OFFSET(tile, _impl_._extensions_) +
          decltype(tile::_impl_._extensions_)::InternalGetArenaOffset(
              ::google::protobuf::MessageLite::internal_visibility()),
      PROTOBUF_FIELD_OFFSET(tile, _impl_.layers_) +
          decltype(tile::_impl_.layers_)::
              InternalGetArenaOffset(
                  ::google::protobuf::MessageLite::internal_visibility()),
  });
  if (arena_bits.has_value()) {
    return ::google::protobuf::internal::MessageCreator::ZeroInit(
        sizeof(tile), alignof(tile), *arena_bits);
  } else {
    return ::google::protobuf::internal::MessageCreator(&tile::PlacementNew_,
                                 sizeof(tile),
                                 alignof(tile));
  }
}
PROTOBUF_CONSTINIT
PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::google::protobuf::internal::ClassDataLite<19> tile::_class_data_ = {
    {
        &_tile_default_instance_._instance,
        &_table_.header,
        nullptr,  // OnDemandRegisterArenaDtor
        tile::IsInitializedImpl,
        &tile::MergeImpl,
        ::google::protobuf::MessageLite::GetNewImpl<tile>(),
#if defined(PROTOBUF_CUSTOM_VTABLE)
        &tile::SharedDtor,
        ::google::protobuf::MessageLite::GetClearImpl<tile>(), &tile::ByteSizeLong,
            &tile::_InternalSerialize,
#endif  // PROTOBUF_CUSTOM_VTABLE
        PROTOBUF_FIELD_OFFSET(tile, _impl_._cached_size_),
        true,
    },
    "mapnik.vector.tile",
};
const ::google::protobuf::internal::ClassData* tile::GetClassData() const {
  return _class_data_.base();
}
PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<0, 1, 1, 0, 2> tile::_table_ = {
  {
    0,  // no _has_bits_
    PROTOBUF_FIELD_OFFSET(tile, _impl_._extensions_),
    3, 0,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294967291,  // skipmap
    offsetof(decltype(_table_), field_entries),
    1,  // num_field_entries
    1,  // num_aux_entries
    offsetof(decltype(_table_), aux_entries),
    _class_data_.base(),
    nullptr,  // post_loop_handler
    ::_pbi::TcParser::GenericFallbackLite,  // fallback
    #ifdef PROTOBUF_PREFETCH_PARSE_TABLE
    ::_pbi::TcParser::GetTable<::mapnik::vector::tile>(),  // to_prefetch
    #endif  // PROTOBUF_PREFETCH_PARSE_TABLE
  }, {{
    // repeated .mapnik.vector.tile.layer layers = 3;
    {::_pbi::TcParser::FastMtR1,
     {26, 63, 0, PROTOBUF_FIELD_OFFSET(tile, _impl_.layers_)}},
  }}, {{
    65535, 65535
  }}, {{
    // repeated .mapnik.vector.tile.layer layers = 3;
    {PROTOBUF_FIELD_OFFSET(tile, _impl_.layers_), 0, 0,
    (0 | ::_fl::kFcRepeated | ::_fl::kMessage | ::_fl::kTvTable)},
  }}, {{
    {::_pbi::TcParser::GetTable<::mapnik::vector::tile_layer>()},
  }}, {{
  }},
};

PROTOBUF_NOINLINE void tile::Clear() {
// @@protoc_insertion_point(message_clear_start:mapnik.vector.tile)
  ::google::protobuf::internal::TSanWrite(&_impl_);
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_._extensions_.Clear();
  _impl_.layers_.Clear();
  _internal_metadata_.Clear<std::string>();
}

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::uint8_t* tile::_InternalSerialize(
            const MessageLite& base, ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) {
          const tile& this_ = static_cast<const tile&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::uint8_t* tile::_InternalSerialize(
            ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) const {
          const tile& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(serialize_to_array_start:mapnik.vector.tile)
          ::uint32_t cached_has_bits = 0;
          (void)cached_has_bits;

          // repeated .mapnik.vector.tile.layer layers = 3;
          for (unsigned i = 0, n = static_cast<unsigned>(
                                   this_._internal_layers_size());
               i < n; i++) {
            const auto& repfield = this_._internal_layers().Get(i);
            target =
                ::google::protobuf::internal::WireFormatLite::InternalWriteMessage(
                    3, repfield, repfield.GetCachedSize(),
                    target, stream);
          }

          // Extension range [16, 8192)
          target = this_._impl_._extensions_._InternalSerialize(
              internal_default_instance(), 16, 8192, target, stream);
          if (PROTOBUF_PREDICT_FALSE(this_._internal_metadata_.have_unknown_fields())) {
            target = stream->WriteRaw(
                this_._internal_metadata_.unknown_fields<std::string>(::google::protobuf::internal::GetEmptyString).data(),
                static_cast<int>(this_._internal_metadata_.unknown_fields<std::string>(::google::protobuf::internal::GetEmptyString).size()), target);
          }
          // @@protoc_insertion_point(serialize_to_array_end:mapnik.vector.tile)
          return target;
        }

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::size_t tile::ByteSizeLong(const MessageLite& base) {
          const tile& this_ = static_cast<const tile&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::size_t tile::ByteSizeLong() const {
          const tile& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(message_byte_size_start:mapnik.vector.tile)
          ::size_t total_size = 0;
          total_size += this_._impl_._extensions_.ByteSize();

          ::uint32_t cached_has_bits = 0;
          // Prevent compiler warnings about cached_has_bits being unused
          (void)cached_has_bits;

          ::_pbi::Prefetch5LinesFrom7Lines(&this_);
           {
            // repeated .mapnik.vector.tile.layer layers = 3;
            {
              total_size += 1UL * this_._internal_layers_size();
              for (const auto& msg : this_._internal_layers()) {
                total_size += ::google::protobuf::internal::WireFormatLite::MessageSize(msg);
              }
            }
          }
          if (PROTOBUF_PREDICT_FALSE(this_._internal_metadata_.have_unknown_fields())) {
            total_size += this_._internal_metadata_.unknown_fields<std::string>(::google::protobuf::internal::GetEmptyString).size();
          }
          this_._impl_._cached_size_.Set(::_pbi::ToCachedSize(total_size));
          return total_size;
        }

void tile::MergeImpl(::google::protobuf::MessageLite& to_msg, const ::google::protobuf::MessageLite& from_msg) {
  auto* const _this = static_cast<tile*>(&to_msg);
  auto& from = static_cast<const tile&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:mapnik.vector.tile)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_internal_mutable_layers()->MergeFrom(
      from._internal_layers());
  _this->_impl_._extensions_.MergeFrom(internal_default_instance(), from._impl_._extensions_);
  _this->_internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
}

void tile::CopyFrom(const tile& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:mapnik.vector.tile)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

PROTOBUF_NOINLINE bool tile::IsInitializedImpl(
    const MessageLite& msg) {
  auto& this_ = static_cast<const tile&>(msg);
  if (!this_._impl_._extensions_.IsInitialized(
          internal_default_instance())) {
    return false;
  }
  if (!::google::protobuf::internal::AllAreInitialized(this_._internal_layers()))
    return false;
  return true;
}

void tile::InternalSwap(tile* PROTOBUF_RESTRICT other) {
  using std::swap;
  _impl_._extensions_.InternalSwap(&other->_impl_._extensions_);
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.layers_.InternalSwap(&other->_impl_.layers_);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace vector
}  // namespace mapnik
namespace google {
namespace protobuf {
}  // namespace protobuf
}  // namespace google
// @@protoc_insertion_point(global_scope)
#include "google/protobuf/port_undef.inc"
