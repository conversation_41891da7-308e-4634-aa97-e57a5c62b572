﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <PropertyGroup>
    <ResolveNugetPackages>false</ResolveNugetPackages>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{5A2E383D-DDD5-36F8-B317-F4ED69148EB5}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>ALL_BUILD</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Midl>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <Midl>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <Midl>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SF:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu -BF:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk --check-stamp-file F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeDependentOption.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindGit.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindOpenGL.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindOpenSceneGraph.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindOpenThreads.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Findosg.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgDB.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgGA.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgManipulator.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgShadow.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgSim.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgText.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgUtil.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgViewer.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Findosg_functions.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\GNUInstallDirs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\WindowsPaths.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\SelectLibraryConfigurations.cmake;C:\dev\vcpkg\scripts\buildsystems\vcpkg.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\CMakeFiles\3.26.4\CMakeCCompiler.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\CMakeFiles\3.26.4\CMakeCXXCompiler.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\CMakeFiles\3.26.4\CMakeRCCompiler.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\CMakeFiles\3.26.4\CMakeSystem.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\cmake\cmake_uninstall.cmake.in;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\cmake\install-package-config-files.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\cmake\oe_ios.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\cmake\oe_osx.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\cmake\oe_unix.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\cmake\oe_win32.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\cmake\osgearth-macros.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\vcpkg.json;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SF:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu -BF:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk --check-stamp-file F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeDependentOption.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindGit.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindOpenGL.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindOpenSceneGraph.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindOpenThreads.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Findosg.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgDB.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgGA.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgManipulator.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgShadow.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgSim.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgText.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgUtil.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgViewer.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Findosg_functions.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\GNUInstallDirs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\WindowsPaths.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\SelectLibraryConfigurations.cmake;C:\dev\vcpkg\scripts\buildsystems\vcpkg.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\CMakeFiles\3.26.4\CMakeCCompiler.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\CMakeFiles\3.26.4\CMakeCXXCompiler.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\CMakeFiles\3.26.4\CMakeRCCompiler.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\CMakeFiles\3.26.4\CMakeSystem.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\cmake\cmake_uninstall.cmake.in;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\cmake\install-package-config-files.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\cmake\oe_ios.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\cmake\oe_osx.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\cmake\oe_unix.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\cmake\oe_win32.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\cmake\osgearth-macros.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\vcpkg.json;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SF:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu -BF:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk --check-stamp-file F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeDependentOption.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindGit.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindOpenGL.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindOpenSceneGraph.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindOpenThreads.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Findosg.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgDB.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgGA.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgManipulator.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgShadow.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgSim.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgText.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgUtil.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgViewer.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Findosg_functions.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\GNUInstallDirs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\WindowsPaths.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\SelectLibraryConfigurations.cmake;C:\dev\vcpkg\scripts\buildsystems\vcpkg.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\CMakeFiles\3.26.4\CMakeCCompiler.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\CMakeFiles\3.26.4\CMakeCXXCompiler.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\CMakeFiles\3.26.4\CMakeRCCompiler.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\CMakeFiles\3.26.4\CMakeSystem.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\cmake\cmake_uninstall.cmake.in;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\cmake\install-package-config-files.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\cmake\oe_ios.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\cmake\oe_osx.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\cmake\oe_unix.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\cmake\oe_win32.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\cmake\osgearth-macros.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\vcpkg.json;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SF:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu -BF:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk --check-stamp-file F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeDependentOption.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindGit.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindOpenGL.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindOpenSceneGraph.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindOpenThreads.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Findosg.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgDB.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgGA.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgManipulator.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgShadow.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgSim.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgText.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgUtil.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgViewer.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Findosg_functions.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\GNUInstallDirs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\WindowsPaths.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\SelectLibraryConfigurations.cmake;C:\dev\vcpkg\scripts\buildsystems\vcpkg.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\CMakeFiles\3.26.4\CMakeCCompiler.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\CMakeFiles\3.26.4\CMakeCXXCompiler.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\CMakeFiles\3.26.4\CMakeRCCompiler.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\CMakeFiles\3.26.4\CMakeSystem.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\cmake\cmake_uninstall.cmake.in;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\cmake\install-package-config-files.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\cmake\oe_ios.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\cmake\oe_osx.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\cmake\oe_unix.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\cmake\oe_win32.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\cmake\osgearth-macros.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\vcpkg.json;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\ZERO_CHECK.vcxproj">
      <Project>{F6C31C9C-B23A-34A9-9FDF-64F2E3F0BC91}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarth\osgEarth.vcxproj">
      <Project>{59F5F234-9F89-3B79-AB4B-2E21EC3563DF}</Project>
      <Name>osgEarth</Name>
    </ProjectReference>
    <ProjectReference Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarthImGui\osgEarthImGui.vcxproj">
      <Project>{3BAC6A5B-C2B7-3357-AEF9-AD2EB0679A02}</Project>
      <Name>osgEarthImGui</Name>
    </ProjectReference>
    <ProjectReference Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarthDrivers\earth\osgdb_earth.vcxproj">
      <Project>{CDF1E421-5CD3-357C-B2F4-E2C5AD6E5859}</Project>
      <Name>osgdb_earth</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarthDrivers\fastdxt\osgdb_fastdxt.vcxproj">
      <Project>{33EA527A-05D5-3906-84BA-A7257E0A796E}</Project>
      <Name>osgdb_fastdxt</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarthDrivers\gltf\osgdb_gltf.vcxproj">
      <Project>{9250A923-FCB4-3A75-B060-C52F4A0B114E}</Project>
      <Name>osgdb_gltf</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarthDrivers\kml\osgdb_kml.vcxproj">
      <Project>{222CA42F-7833-3DC6-9BD6-DEBA4C6B70A6}</Project>
      <Name>osgdb_kml</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarthDrivers\bumpmap\osgdb_osgearth_bumpmap.vcxproj">
      <Project>{B11EDDAC-94B0-36C7-BB3E-E613DC3BD793}</Project>
      <Name>osgdb_osgearth_bumpmap</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarthDrivers\cache_filesystem\osgdb_osgearth_cache_filesystem.vcxproj">
      <Project>{330C5B10-F1C6-3774-AB50-18BED8A42287}</Project>
      <Name>osgdb_osgearth_cache_filesystem</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarthDrivers\cache_rocksdb\osgdb_osgearth_cache_rocksdb.vcxproj">
      <Project>{686D33AD-39F4-3F0C-9E02-8E980697B0A4}</Project>
      <Name>osgdb_osgearth_cache_rocksdb</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarthDrivers\colorramp\osgdb_osgearth_colorramp.vcxproj">
      <Project>{DD61BFCD-008B-331D-8BC0-3BD0AB3C7901}</Project>
      <Name>osgdb_osgearth_colorramp</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarthDrivers\detail\osgdb_osgearth_detail.vcxproj">
      <Project>{4150FE74-2AAD-3A1A-86C9-4EB05162BA29}</Project>
      <Name>osgdb_osgearth_detail</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarthDrivers\engine_rex\osgdb_osgearth_engine_rex.vcxproj">
      <Project>{D99AE25A-E939-3E28-BC86-E00065C8199E}</Project>
      <Name>osgdb_osgearth_engine_rex</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarthDrivers\featurefilter_intersect\osgdb_osgearth_featurefilter_intersect.vcxproj">
      <Project>{B3054C90-46D5-3995-A93C-80598E233A96}</Project>
      <Name>osgdb_osgearth_featurefilter_intersect</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarthDrivers\featurefilter_join\osgdb_osgearth_featurefilter_join.vcxproj">
      <Project>{970CF942-396E-3D15-9ACC-5AE9E31593A1}</Project>
      <Name>osgdb_osgearth_featurefilter_join</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarthDrivers\script_engine_duktape\osgdb_osgearth_scriptengine_javascript.vcxproj">
      <Project>{A1BC37CD-69E8-363D-98D6-5677CD3C5A55}</Project>
      <Name>osgdb_osgearth_scriptengine_javascript</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarthDrivers\sky_gl\osgdb_osgearth_sky_gl.vcxproj">
      <Project>{2EB2A980-8FF0-304F-92C7-7737E46B3CA2}</Project>
      <Name>osgdb_osgearth_sky_gl</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarthDrivers\sky_simple\osgdb_osgearth_sky_simple.vcxproj">
      <Project>{FF029547-7E27-3DD2-AC52-CBE46AEDF0B4}</Project>
      <Name>osgdb_osgearth_sky_simple</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarthDrivers\terrainshader\osgdb_osgearth_terrainshader.vcxproj">
      <Project>{3978AE22-C9D1-3165-82A0-1CB32BAE56FA}</Project>
      <Name>osgdb_osgearth_terrainshader</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarthDrivers\vdatum_egm2008\osgdb_osgearth_vdatum_egm2008.vcxproj">
      <Project>{440D2D8D-17A4-3F5F-A9E0-633CCBCA8522}</Project>
      <Name>osgdb_osgearth_vdatum_egm2008</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarthDrivers\vdatum_egm84\osgdb_osgearth_vdatum_egm84.vcxproj">
      <Project>{D545476B-DAA4-3998-9DCA-E403B2B11984}</Project>
      <Name>osgdb_osgearth_vdatum_egm84</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarthDrivers\vdatum_egm96\osgdb_osgearth_vdatum_egm96.vcxproj">
      <Project>{C631CA43-2C60-37A6-847F-D5C9804A9AF3}</Project>
      <Name>osgdb_osgearth_vdatum_egm96</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarthDrivers\viewpoints\osgdb_osgearth_viewpoints.vcxproj">
      <Project>{FCB2FFFB-4849-301D-A42E-DBE311F84F64}</Project>
      <Name>osgdb_osgearth_viewpoints</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarthDrivers\template\osgdb_template.vcxproj">
      <Project>{A0B69E0A-3578-3327-BD24-3BADA7EAE2B8}</Project>
      <Name>osgdb_template</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarthDrivers\webp\osgdb_webp.vcxproj">
      <Project>{F238A63B-7FCE-357F-9BC0-1E15BA838759}</Project>
      <Name>osgdb_webp</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarthDrivers\zip\osgdb_zip.vcxproj">
      <Project>{7E5FECBF-0880-37F9-A907-7DFC5A878362}</Project>
      <Name>osgdb_zip</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\applications\osgearth_3pv\osgearth_3pv.vcxproj">
      <Project>{F7931B1F-C079-3021-9869-20492226F41B}</Project>
      <Name>osgearth_3pv</Name>
    </ProjectReference>
    <ProjectReference Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\applications\osgearth_annotation\osgearth_annotation.vcxproj">
      <Project>{6B0273B9-798A-3631-8BAB-6FFDC2B92404}</Project>
      <Name>osgearth_annotation</Name>
    </ProjectReference>
    <ProjectReference Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\applications\osgearth_atlas\osgearth_atlas.vcxproj">
      <Project>{34B660E2-9D32-3132-AA4E-2BDED676F7AE}</Project>
      <Name>osgearth_atlas</Name>
    </ProjectReference>
    <ProjectReference Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\applications\osgearth_bakefeaturetiles\osgearth_bakefeaturetiles.vcxproj">
      <Project>{5B7B6ED8-E833-3C79-AABB-2AFA9E6577D4}</Project>
      <Name>osgearth_bakefeaturetiles</Name>
    </ProjectReference>
    <ProjectReference Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\applications\osgearth_boundarygen\osgearth_boundarygen.vcxproj">
      <Project>{E2EC9D7B-E04F-3145-88F0-693965885BCE}</Project>
      <Name>osgearth_boundarygen</Name>
    </ProjectReference>
    <ProjectReference Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\applications\osgearth_clamp\osgearth_clamp.vcxproj">
      <Project>{4C191943-F78B-314D-A1EE-3202EDB11946}</Project>
      <Name>osgearth_clamp</Name>
    </ProjectReference>
    <ProjectReference Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\applications\osgearth_conv\osgearth_conv.vcxproj">
      <Project>{389E1961-D0F2-367D-B1D2-C97AA66BDCD6}</Project>
      <Name>osgearth_conv</Name>
    </ProjectReference>
    <ProjectReference Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\applications\osgearth_featurefilter\osgearth_featurefilter.vcxproj">
      <Project>{F4B593D1-6528-3A2E-80C7-46F5296728B7}</Project>
      <Name>osgearth_featurefilter</Name>
    </ProjectReference>
    <ProjectReference Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\applications\osgearth_features\osgearth_features.vcxproj">
      <Project>{A7CCF8D3-580E-34F5-A095-ACD19E07CE93}</Project>
      <Name>osgearth_features</Name>
    </ProjectReference>
    <ProjectReference Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\applications\osgearth_heatmap\osgearth_heatmap.vcxproj">
      <Project>{41A24264-CC49-386F-A7B4-083E1D4A03E3}</Project>
      <Name>osgearth_heatmap</Name>
    </ProjectReference>
    <ProjectReference Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\applications\osgearth_imgui\osgearth_imgui.vcxproj">
      <Project>{637CC17E-0719-3AAF-8445-DC2053204494}</Project>
      <Name>osgearth_imgui</Name>
    </ProjectReference>
    <ProjectReference Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\applications\osgearth_infinitescroll\osgearth_infinitescroll.vcxproj">
      <Project>{E4543BC7-80BA-3F93-854E-48B1D8A3DA77}</Project>
      <Name>osgearth_infinitescroll</Name>
    </ProjectReference>
    <ProjectReference Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\applications\osgearth_los\osgearth_los.vcxproj">
      <Project>{DAA0FF1F-198E-3A94-BD17-EEC2B2470E07}</Project>
      <Name>osgearth_los</Name>
    </ProjectReference>
    <ProjectReference Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\applications\osgearth_map\osgearth_map.vcxproj">
      <Project>{C7D26459-74BF-3143-B1FC-1AB136F251DC}</Project>
      <Name>osgearth_map</Name>
    </ProjectReference>
    <ProjectReference Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\applications\osgearth_minimap\osgearth_minimap.vcxproj">
      <Project>{30F78374-4A27-3DD2-8CA4-E60EFAE685B9}</Project>
      <Name>osgearth_minimap</Name>
    </ProjectReference>
    <ProjectReference Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\applications\osgearth_mrt\osgearth_mrt.vcxproj">
      <Project>{AA062478-31F8-3451-98ED-C1220B6759F5}</Project>
      <Name>osgearth_mrt</Name>
    </ProjectReference>
    <ProjectReference Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\applications\osgearth_myviewer\osgearth_myviewer.vcxproj">
      <Project>{133D6C10-4685-3997-A577-194393BD4464}</Project>
      <Name>osgearth_myviewer</Name>
    </ProjectReference>
    <ProjectReference Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\applications\osgearth_occlusionculling\osgearth_occlusionculling.vcxproj">
      <Project>{2FB6B247-09A2-32F5-8F5C-F90CD91BA895}</Project>
      <Name>osgearth_occlusionculling</Name>
    </ProjectReference>
    <ProjectReference Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\applications\osgearth_simple\osgearth_simple.vcxproj">
      <Project>{404A1DCA-F494-3133-984F-D22057A31BC4}</Project>
      <Name>osgearth_simple</Name>
    </ProjectReference>
    <ProjectReference Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\applications\osgearth_skyview\osgearth_skyview.vcxproj">
      <Project>{0EF861A0-DC36-3CF4-8A93-5319BB0CFBA1}</Project>
      <Name>osgearth_skyview</Name>
    </ProjectReference>
    <ProjectReference Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\applications\osgearth_terrainprofile\osgearth_terrainprofile.vcxproj">
      <Project>{215F8063-EEDD-35E7-8516-72898124D8BF}</Project>
      <Name>osgearth_terrainprofile</Name>
    </ProjectReference>
    <ProjectReference Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\applications\osgearth_tfs\osgearth_tfs.vcxproj">
      <Project>{3F140B6C-51D4-3D6D-AB24-55FEB5C8E0E5}</Project>
      <Name>osgearth_tfs</Name>
    </ProjectReference>
    <ProjectReference Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\applications\osgearth_version\osgearth_version.vcxproj">
      <Project>{08AEF79F-2856-3486-8650-A9E81D511225}</Project>
      <Name>osgearth_version</Name>
    </ProjectReference>
    <ProjectReference Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\applications\osgearth_video\osgearth_video.vcxproj">
      <Project>{EE5F9CD8-90BC-32B6-9B24-54D504651778}</Project>
      <Name>osgearth_video</Name>
    </ProjectReference>
    <ProjectReference Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\applications\osgearth_viewer\osgearth_viewer.vcxproj">
      <Project>{0F815E93-BDB9-360A-B970-58E6229291FE}</Project>
      <Name>osgearth_viewer</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>