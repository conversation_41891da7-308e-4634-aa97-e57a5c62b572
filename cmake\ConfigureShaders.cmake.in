# configureshaders.cmake.in

set(source_dir      "@CMAKE_CURRENT_SOURCE_DIR@")
set(bin_dir         "@CMAKE_CURRENT_BINARY_DIR@")
set(glsl_files      "@GLSL_FILES@")
set(template_file   "@TEMPLATE_FILE@")
set(output_cpp_file "@OUTPUT_CPP_FILE@")

# modify the contents for inlining; replace input with output (var: file)
# i.e., the file name (in the form @my_file_name@) gets replaced with the
# actual contents of the named file and then processed a bit.
foreach(file ${glsl_files})

    # read the file into 'contents':
    file(READ ${source_dir}/${file} contents)

    # compress whitespace.
    # "contents" must be quoted, otherwise semicolons get dropped for some reason.
    string(REGEX REPLACE "\n\n+" "\n" tempString "${contents}")
    
    set(${file} "\nR\"(${tempString})\"")

endforeach(file)

# send the processed glsl_files to the next template to create the
# shader source file.
configure_file(
	${source_dir}/${template_file}
	${output_cpp_file}
	@ONLY )
