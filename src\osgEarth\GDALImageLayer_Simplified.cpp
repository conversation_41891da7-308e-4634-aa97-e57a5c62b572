/* -*-c++-*- */
/* osgEarth - Geospatial SDK for OpenSceneGraph
 * Copyright 2025 Pelican Mapping
 * http://osgearth.org
 *
 * Simplified GDAL layer implementations without GDAL dependencies
 */
#include <osgEarth/GDALImageLayer_Simplified>
#include <osgEarth/Notify>

using namespace osgEarth;

#define LC "[GDALImageLayer] "

REGISTER_OSGEARTH_LAYER(gdalimage, GDALImageLayer);
REGISTER_OSGEARTH_LAYER(gdalelevation, GDALElevationLayer);

//........................................................................
// GDALImageLayer

Config GDALImageLayer::Options::getConfig() const
{
    Config conf = ImageLayer::Options::getConfig();
    conf.set("url", _url);
    conf.set("connection", _connection);
    conf.set("subdataset", _subdataset);
    conf.set("sub_dataset", _subDataSet);
    conf.set("interpolate_on_read", _interpolateOnRead);
    conf.set("gdal_driver", _gdalDriver);
    return conf;
}

void GDALImageLayer::Options::fromConfig(const Config& conf)
{
    conf.get("url", _url);
    conf.get("connection", _connection);
    conf.get("subdataset", _subdataset);
    conf.get("sub_dataset", _subDataSet);
    conf.get("interpolate_on_read", _interpolateOnRead);
    conf.get("gdal_driver", _gdalDriver);
}

GDALImageLayer::~GDALImageLayer()
{
    // No cleanup needed
}

void GDALImageLayer::init()
{
    ImageLayer::init();
    OE_WARN << LC << "GDALImageLayer not supported in simplified build" << std::endl;
}

void GDALImageLayer::setURL(const URI& value)
{
    options().url() = value;
}

const URI& GDALImageLayer::getURL() const
{
    return options().url().get();
}

void GDALImageLayer::setConnection(const std::string& value)
{
    options().connection() = value;
}

const std::string& GDALImageLayer::getConnection() const
{
    return options().connection().get();
}

void GDALImageLayer::setSubDataSet(const int& value)
{
    options().subDataSet() = value;
}

const int& GDALImageLayer::getSubDataSet() const
{
    return options().subDataSet().get();
}

Status GDALImageLayer::openImplementation()
{
    return Status::Error("GDALImageLayer not supported in simplified build");
}

Status GDALImageLayer::closeImplementation()
{
    return Status::OK();
}

GeoImage GDALImageLayer::createImageImplementation(const TileKey& key, ProgressCallback* progress) const
{
    return GeoImage::INVALID;
}

//........................................................................
// GDALElevationLayer

Config GDALElevationLayer::Options::getConfig() const
{
    Config conf = ElevationLayer::Options::getConfig();
    conf.set("url", _url);
    conf.set("connection", _connection);
    conf.set("subdataset", _subdataset);
    conf.set("sub_dataset", _subDataSet);
    conf.set("interpolate_on_read", _interpolateOnRead);
    conf.set("gdal_driver", _gdalDriver);
    return conf;
}

void GDALElevationLayer::Options::fromConfig(const Config& conf)
{
    conf.get("url", _url);
    conf.get("connection", _connection);
    conf.get("subdataset", _subdataset);
    conf.get("sub_dataset", _subDataSet);
    conf.get("interpolate_on_read", _interpolateOnRead);
    conf.get("gdal_driver", _gdalDriver);
}

GDALElevationLayer::~GDALElevationLayer()
{
    // No cleanup needed
}

void GDALElevationLayer::init()
{
    ElevationLayer::init();
    OE_WARN << LC << "GDALElevationLayer not supported in simplified build" << std::endl;
}

void GDALElevationLayer::setURL(const URI& value)
{
    options().url() = value;
}

const URI& GDALElevationLayer::getURL() const
{
    return options().url().get();
}

Status GDALElevationLayer::openImplementation()
{
    return Status::Error("GDALElevationLayer not supported in simplified build");
}

Status GDALElevationLayer::closeImplementation()
{
    return Status::OK();
}

GeoHeightField GDALElevationLayer::createHeightFieldImplementation(const TileKey& key, ProgressCallback* progress) const
{
    return GeoHeightField::INVALID;
}
