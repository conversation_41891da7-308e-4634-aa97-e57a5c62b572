# osgEarth依赖分析和解决方案

## 1. 当前编译的osgEarth版本信息

### 版本详情
- **osgEarth版本**: 3.7.2
- **C++标准**: C++14 (CMakeLists.txt第33行设置)
- **库类型**: **动态库 (SHARED)** - 默认配置 `OSGEARTH_BUILD_SHARED_LIBS=ON`

### 依赖库状态分析

#### ✅ 已配置的依赖
- **OpenSceneGraph**: 3.6.5 (必需，3D渲染引擎)
- **GEOS**: 3.13.0+ (几何操作库)
- **CURL**: 网络请求库
- **SQLite3**: MBTiles支持
- **blend2d**: 矢量渲染 (可选)
- **spdlog**: 日志库 (可选)
- **meshoptimizer**: 网格优化 (可选)

#### ❌ 依赖问题分析
- **GDAL/PROJ依赖**: 虽然在CMakeLists.txt中添加了`find_package(GDAL REQUIRED)`，但代码中仍有大量GDAL/OGR/PROJ相关的函数调用未实现
- **geographiclib依赖**: 未添加到构建系统中

### 编译错误分析

从编译输出可以看出，当前项目存在**78个无法解析的外部符号**，主要包括：

1. **SpatialReference相关错误** (最多)：
   - `SpatialReference::create()`
   - `SpatialReference::transform2D()`
   - `SpatialReference::getBounds()`
   - `SpatialReference::transformUnits()`
   - 等等...

2. **GDAL图层相关错误**：
   - `GDALImageLayer::init()`
   - `GDALImageLayer::openImplementation()`
   - `GDALElevationLayer::init()`
   - 等等...

3. **OGR要素源相关错误**：
   - `OGRFeatureSource::init()`
   - `OGRFeatureSource::openImplementation()`
   - 各种OGR_*函数调用
   - 等等...

## 2. 问题根本原因

**核心问题**: 当前的"简化"版本实际上并没有真正移除GDAL/PROJ依赖，而是：
- 在链接时移除了这些库的链接
- 但代码中仍然大量使用这些库的API
- 导致编译时找不到这些函数的实现

这种方法是**不可行的**，因为osgEarth的核心功能（坐标转换、投影、地理数据处理）严重依赖GDAL/PROJ。

## 3. 解决方案建议

### 推荐方案：使用原版osgEarth + 完整依赖

考虑到您需要：
- XYZ图层支持
- .earth文件支持  
- 坐标转换和投影功能
- 完整的地理数据处理能力

**最实际的方案是使用原版osgEarth，包含所有必要的依赖**：

#### 依赖库配置
```cmake
# 必需依赖
find_package(OpenSceneGraph REQUIRED)
find_package(GDAL REQUIRED)           # 地理数据抽象库
find_package(PROJ REQUIRED)           # 投影库  
find_package(GEOS REQUIRED)           # 几何操作库
find_package(CURL REQUIRED)           # 网络库
find_package(SQLite3 REQUIRED)        # 数据库库

# 可选依赖
find_package(geographiclib QUIET)     # 地理计算库
```

#### 库类型建议
- **动态库 (SHARED)**: 便于调试和更新
- **静态库 (STATIC)**: 便于部署，但体积较大

## 4. osgearth_myviewer实现方案

### 正确的实现方式

```cpp
// 使用完整的osgEarth API
#include <osgEarth/EarthManipulator>
#include <osgEarth/Map>
#include <osgEarth/MapNode>
#include <osgEarth/XYZ>
#include <osgEarth/GeodeticGraticule>

// 创建XYZ图层
XYZImageLayer::Options imageOptions;
imageOptions.url() = "http://mt1.google.com/vt/lyrs=s&x={x}&y={y}&z={z}";
imageOptions.profile() = Registry::instance()->getSphericalMercatorProfile();

osg::ref_ptr<XYZImageLayer> imageLayer = new XYZImageLayer(imageOptions);
map->addLayer(imageLayer);
```

### 网络代理支持
```cpp
// 配置网络代理 127.0.0.1:10809
HTTPClient::setProxyHost("127.0.0.1");
HTTPClient::setProxyPort(10809);
```

## 5. 技术架构建议

### 当前状态总结
- ✅ **vcpkg依赖管理**: 已正确配置，包含所有必要的库
- ✅ **OSG集成**: OpenSceneGraph 3.6.5正常工作
- ❌ **GDAL/PROJ集成**: 需要恢复完整的依赖和实现
- ❌ **geographiclib集成**: 需要添加到构建系统

### 建议的技术栈
1. **3D渲染**: OpenSceneGraph 3.6.5
2. **地理数据**: GDAL 3.11.0 (包含OGR)
3. **坐标投影**: PROJ 9.6.2
4. **几何操作**: GEOS 3.13.0
5. **地理计算**: geographiclib (推荐添加)
6. **网络请求**: CURL
7. **数据存储**: SQLite3

## 6. 下一步行动计划

1. **恢复原版osgEarth配置**
   - 恢复GDAL/PROJ依赖
   - 确保所有必要的实现代码存在

2. **添加geographiclib支持**
   - 在CMakeLists.txt中添加find_package(geographiclib)
   - 集成地理计算功能

3. **完善osgearth_myviewer**
   - 使用完整的osgEarth API
   - 实现XYZ图层加载
   - 支持.earth文件
   - 添加网络代理支持

4. **测试和验证**
   - 编译完整的osgEarth库
   - 测试XYZ图层功能
   - 验证坐标转换和投影

## 7. 结论

**当前的"简化"方法不可行**。osgEarth的核心功能与GDAL/PROJ紧密耦合，移除这些依赖会导致大量功能缺失。

**推荐使用原版osgEarth + 完整依赖**，这样可以：
- 获得完整的地理数据处理能力
- 支持所有XYZ图层格式
- 支持.earth配置文件
- 提供完整的坐标转换和投影功能
- 确保长期维护和兼容性

虽然依赖库较多，但通过vcpkg可以很好地管理这些依赖，而且现代硬件完全可以承受这些库的开销。
