# This is the CMakeCache file.
# For build in directory: f:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk
# It was generated by CMake: C:/Program Files/CMake/bin/cmake.exe
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//Append the OSG version number to the osgPlugins directory
APPEND_OPENSCENEGRAPH_VERSION:BOOL=ON

//Path to a program.
CMAKE_AR:FILEPATH=C:/Program Files/Microsoft Visual Studio/2022/Enterprise/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/lib.exe

//Semicolon separated list of supported configuration types, only
// supports Debug, Release, MinSizeRel, and RelWithDebInfo, anything
// else will be ignored.
CMAKE_CONFIGURATION_TYPES:STRING=Debug;Release;MinSizeRel;RelWithDebInfo

//Flags used by the CXX compiler during all build types.
CMAKE_CXX_FLAGS:STRING=/DWIN32 /D_WINDOWS /EHsc

//Flags used by the CXX compiler during DEBUG builds.
CMAKE_CXX_FLAGS_DEBUG:STRING=/Zi /Ob0 /Od /RTC1

//Flags used by the CXX compiler during MINSIZEREL builds.
CMAKE_CXX_FLAGS_MINSIZEREL:STRING=/O1 /Ob1 /DNDEBUG

//Flags used by the CXX compiler during RELEASE builds.
CMAKE_CXX_FLAGS_RELEASE:STRING=/O2 /Ob2 /DNDEBUG

//Flags used by the CXX compiler during RELWITHDEBINFO builds.
CMAKE_CXX_FLAGS_RELWITHDEBINFO:STRING=/Zi /O2 /Ob1 /DNDEBUG

//Libraries linked by default with all C++ applications.
CMAKE_CXX_STANDARD_LIBRARIES:STRING=kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib

//Flags used by the C compiler during all build types.
CMAKE_C_FLAGS:STRING=/DWIN32 /D_WINDOWS

//Flags used by the C compiler during DEBUG builds.
CMAKE_C_FLAGS_DEBUG:STRING=/Zi /Ob0 /Od /RTC1

//Flags used by the C compiler during MINSIZEREL builds.
CMAKE_C_FLAGS_MINSIZEREL:STRING=/O1 /Ob1 /DNDEBUG

//Flags used by the C compiler during RELEASE builds.
CMAKE_C_FLAGS_RELEASE:STRING=/O2 /Ob2 /DNDEBUG

//Flags used by the C compiler during RELWITHDEBINFO builds.
CMAKE_C_FLAGS_RELWITHDEBINFO:STRING=/Zi /O2 /Ob1 /DNDEBUG

//Libraries linked by default with all C applications.
CMAKE_C_STANDARD_LIBRARIES:STRING=kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib

//add a Debug postfix, usually d on windows
CMAKE_DEBUG_POSTFIX:STRING=d

//Flags used by the linker during all build types.
CMAKE_EXE_LINKER_FLAGS:STRING=/machine:x64

//Flags used by the linker during DEBUG builds.
CMAKE_EXE_LINKER_FLAGS_DEBUG:STRING=/debug /INCREMENTAL

//Flags used by the linker during MINSIZEREL builds.
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL:STRING=/INCREMENTAL:NO

//Flags used by the linker during RELEASE builds.
CMAKE_EXE_LINKER_FLAGS_RELEASE:STRING=/INCREMENTAL:NO

//Flags used by the linker during RELWITHDEBINFO builds.
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO:STRING=/debug /INCREMENTAL

//Value Computed by CMake.
CMAKE_FIND_PACKAGE_REDIRECTS_DIR:STATIC=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/CMakeFiles/pkgRedirects

//User executables (bin)
CMAKE_INSTALL_BINDIR:PATH=bin

//Read-only architecture-independent data (DATAROOTDIR)
CMAKE_INSTALL_DATADIR:PATH=

//Read-only architecture-independent data root (share)
CMAKE_INSTALL_DATAROOTDIR:PATH=share

//Documentation root (DATAROOTDIR/doc/PROJECT_NAME)
CMAKE_INSTALL_DOCDIR:PATH=

//C header files (include)
CMAKE_INSTALL_INCLUDEDIR:PATH=include

//Info documentation (DATAROOTDIR/info)
CMAKE_INSTALL_INFODIR:PATH=

//Object code libraries (lib)
CMAKE_INSTALL_LIBDIR:PATH=lib

//Program executables (libexec)
CMAKE_INSTALL_LIBEXECDIR:PATH=libexec

//Locale-dependent data (DATAROOTDIR/locale)
CMAKE_INSTALL_LOCALEDIR:PATH=

//Modifiable single-machine data (var)
CMAKE_INSTALL_LOCALSTATEDIR:PATH=var

//Man documentation (DATAROOTDIR/man)
CMAKE_INSTALL_MANDIR:PATH=

//C header files for non-gcc (/usr/include)
CMAKE_INSTALL_OLDINCLUDEDIR:PATH=/usr/include

//Install path prefix, prepended onto install directories.
CMAKE_INSTALL_PREFIX:PATH=C:/Program Files (x86)/OSGEARTH

//Run-time variable data (LOCALSTATEDIR/run)
CMAKE_INSTALL_RUNSTATEDIR:PATH=

//System admin executables (sbin)
CMAKE_INSTALL_SBINDIR:PATH=sbin

//Modifiable architecture-independent data (com)
CMAKE_INSTALL_SHAREDSTATEDIR:PATH=com

//Read-only single-machine data (etc)
CMAKE_INSTALL_SYSCONFDIR:PATH=etc

//Path to a program.
CMAKE_LINKER:FILEPATH=C:/Program Files/Microsoft Visual Studio/2022/Enterprise/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/link.exe

//add a MinSizeRel postfix, usually empty on windows
CMAKE_MINSIZEREL_POSTFIX:STRING=s

//Flags used by the linker during the creation of modules during
// all build types.
CMAKE_MODULE_LINKER_FLAGS:STRING=/machine:x64

//Flags used by the linker during the creation of modules during
// DEBUG builds.
CMAKE_MODULE_LINKER_FLAGS_DEBUG:STRING=/debug /INCREMENTAL

//Flags used by the linker during the creation of modules during
// MINSIZEREL builds.
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL:STRING=/INCREMENTAL:NO

//Flags used by the linker during the creation of modules during
// RELEASE builds.
CMAKE_MODULE_LINKER_FLAGS_RELEASE:STRING=/INCREMENTAL:NO

//Flags used by the linker during the creation of modules during
// RELWITHDEBINFO builds.
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO:STRING=/debug /INCREMENTAL

//Path to a program.
CMAKE_MT:FILEPATH=CMAKE_MT-NOTFOUND

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=osgEarth SDK

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=https://github.com/gwaldron/osgearth

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=OSGEARTH

//RC compiler
CMAKE_RC_COMPILER:FILEPATH=C:/Program Files (x86)/Embarcadero/Studio/21.0/bin/rc.exe

//Flags for Windows Resource Compiler during all build types.
CMAKE_RC_FLAGS:STRING=-DWIN32

//Flags for Windows Resource Compiler during DEBUG builds.
CMAKE_RC_FLAGS_DEBUG:STRING=-D_DEBUG

//Flags for Windows Resource Compiler during MINSIZEREL builds.
CMAKE_RC_FLAGS_MINSIZEREL:STRING=

//Flags for Windows Resource Compiler during RELEASE builds.
CMAKE_RC_FLAGS_RELEASE:STRING=

//Flags for Windows Resource Compiler during RELWITHDEBINFO builds.
CMAKE_RC_FLAGS_RELWITHDEBINFO:STRING=

//add a Release postfix, usually empty on windows
CMAKE_RELEASE_POSTFIX:STRING=

//add a RelWithDebInfo postfix, usually empty on windows
CMAKE_RELWITHDEBINFO_POSTFIX:STRING=

//Flags used by the linker during the creation of shared libraries
// during all build types.
CMAKE_SHARED_LINKER_FLAGS:STRING=/machine:x64

//Flags used by the linker during the creation of shared libraries
// during DEBUG builds.
CMAKE_SHARED_LINKER_FLAGS_DEBUG:STRING=/debug /INCREMENTAL

//Flags used by the linker during the creation of shared libraries
// during MINSIZEREL builds.
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL:STRING=/INCREMENTAL:NO

//Flags used by the linker during the creation of shared libraries
// during RELEASE builds.
CMAKE_SHARED_LINKER_FLAGS_RELEASE:STRING=/INCREMENTAL:NO

//Flags used by the linker during the creation of shared libraries
// during RELWITHDEBINFO builds.
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO:STRING=/debug /INCREMENTAL

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=NO

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=NO

//Flags used by the linker during the creation of static libraries
// during all build types.
CMAKE_STATIC_LINKER_FLAGS:STRING=/machine:x64

//Flags used by the linker during the creation of static libraries
// during DEBUG builds.
CMAKE_STATIC_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of static libraries
// during MINSIZEREL builds.
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of static libraries
// during RELEASE builds.
CMAKE_STATIC_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of static libraries
// during RELWITHDEBINFO builds.
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//No help, variable specified on the command line.
CMAKE_TOOLCHAIN_FILE:UNINITIALIZED=C:/dev/vcpkg/scripts/buildsystems/vcpkg.cmake

//If this value is on, makefiles will be generated without the
// .SILENT directive, and all commands will be echoed to the console
// during the make.  This is useful for debugging only. With Visual
// Studio IDE projects all commands are done without /nologo.
CMAKE_VERBOSE_MAKEFILE:BOOL=FALSE

//The directory containing a CMake configuration file for CURL.
CURL_DIR:PATH=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/share/curl

//The directory containing a CMake configuration file for GDAL.
GDAL_DIR:PATH=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/share/gdal

//Git command line client
GIT_EXECUTABLE:FILEPATH=C:/Program Files/Git/cmd/git.exe

//The directory containing a CMake configuration file for GLEW.
GLEW_DIR:PATH=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/share/glew

//The directory containing a CMake configuration file for LibZip.
LibZip_DIR:PATH=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/share/libzip

//OpenGL library for win32
OPENGL_gl_LIBRARY:STRING=opengl32

//GLU library for win32
OPENGL_glu_LIBRARY:STRING=glu32

//Path to a file.
OPENTHREADS_INCLUDE_DIR:PATH=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/include

//Path to a library.
OPENTHREADS_LIBRARY_DEBUG:FILEPATH=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/debug/lib/OpenThreadsd.lib

//Path to a library.
OPENTHREADS_LIBRARY_RELEASE:FILEPATH=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/lib/OpenThreads.lib

//Path to a file.
OSGDB_INCLUDE_DIR:PATH=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/include

//Path to a library.
OSGDB_LIBRARY_DEBUG:FILEPATH=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/debug/lib/osgDBd.lib

//Path to a library.
OSGDB_LIBRARY_RELEASE:FILEPATH=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/lib/osgDB.lib

//Assume the use of a single GL context for all GL objects (advanced)
OSGEARTH_ASSUME_SINGLE_GL_CONTEXT:BOOL=OFF

//Assume OSG will always be configured to run in SingleThreaded
// mode (advanced)
OSGEARTH_ASSUME_SINGLE_THREADED_OSG:BOOL=OFF

//Value Computed by CMake
OSGEARTH_BINARY_DIR:STATIC=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk

//Build the Cesium nodekit (osgEarthCesium)
OSGEARTH_BUILD_CESIUM_NODEKIT:BOOL=OFF

//Include the documentation folder
OSGEARTH_BUILD_DOCS:BOOL=ON

//Build the osgEarth example applications
OSGEARTH_BUILD_EXAMPLES:BOOL=ON

//Build the osgEarth ImGui nodekit and ImGui-based apps
OSGEARTH_BUILD_IMGUI_NODEKIT:BOOL=ON

//Build the legacy Controls UI API
OSGEARTH_BUILD_LEGACY_CONTROLS_API:BOOL=OFF

//Build the legacy procedural nodekit (osgEarthSplat)
OSGEARTH_BUILD_LEGACY_SPLAT_NODEKIT:BOOL=OFF

//Build the procedural terrain nodekit (osgEarthProcedural)
OSGEARTH_BUILD_PROCEDURAL_NODEKIT:BOOL=OFF

//ON to build shared libraries; OFF to build static libraries.
OSGEARTH_BUILD_SHARED_LIBS:BOOL=ON

//Build support for SunDog SilverLining SDK
OSGEARTH_BUILD_SILVERLINING_NODEKIT:BOOL=OFF

//Build the osgEarth unit tests
OSGEARTH_BUILD_TESTS:BOOL=OFF

//Build the osgEarth command-line tools
OSGEARTH_BUILD_TOOLS:BOOL=ON

//Build support for SunDog Triton SDK
OSGEARTH_BUILD_TRITON_NODEKIT:BOOL=OFF

//Build osgEarth's zip plugin based on libzip
OSGEARTH_BUILD_ZIP_PLUGIN:BOOL=ON

//Set to ON to build optional FastDXT image compressor.
OSGEARTH_ENABLE_FASTDXT:BOOL=ON

//Enable the geocoder (GDAL/OGR must be built with geocoder support)
OSGEARTH_ENABLE_GEOCODER:BOOL=OFF

//Enable profiling with Tracy
OSGEARTH_ENABLE_PROFILING:BOOL=OFF

//Whether to use the WinInet library for HTTP requests (instead
// of cURL)
OSGEARTH_ENABLE_WININET_FOR_HTTP:BOOL=OFF

//Location of OpenGL CORE profile header parent folder
OSGEARTH_GLCORE_INCLUDE_DIR:PATH=

//osgEarth CMake package install directory
OSGEARTH_INSTALL_CMAKEDIR:STRING=lib/cmake/osgearth

//osgEarth data directory
OSGEARTH_INSTALL_DATADIR:STRING=share/osgearth

//Whether to deploy Windows .pdb files
OSGEARTH_INSTALL_PDBS:BOOL=OFF

//Parent folder of OSG plugins folder
OSGEARTH_INSTALL_PLUGINSDIR:STRING=bin

//Whether to deploy GLSL shaders when installing (OFF=inlined shaders)
OSGEARTH_INSTALL_SHADERS:BOOL=OFF

//Value Computed by CMake
OSGEARTH_IS_TOP_LEVEL:STATIC=ON

//ON to append so-version numbers to libraries
OSGEARTH_SONAMES:BOOL=ON

//Value Computed by CMake
OSGEARTH_SOURCE_DIR:STATIC=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu

//Path to a file.
OSGGA_INCLUDE_DIR:PATH=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/include

//Path to a library.
OSGGA_LIBRARY_DEBUG:FILEPATH=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/debug/lib/osgGAd.lib

//Path to a library.
OSGGA_LIBRARY_RELEASE:FILEPATH=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/lib/osgGA.lib

//Path to a file.
OSGMANIPULATOR_INCLUDE_DIR:PATH=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/include

//Path to a library.
OSGMANIPULATOR_LIBRARY_DEBUG:FILEPATH=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/debug/lib/osgManipulatord.lib

//Path to a library.
OSGMANIPULATOR_LIBRARY_RELEASE:FILEPATH=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/lib/osgManipulator.lib

//Path to a file.
OSGSHADOW_INCLUDE_DIR:PATH=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/include

//Path to a library.
OSGSHADOW_LIBRARY_DEBUG:FILEPATH=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/debug/lib/osgShadowd.lib

//Path to a library.
OSGSHADOW_LIBRARY_RELEASE:FILEPATH=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/lib/osgShadow.lib

//Path to a file.
OSGSIM_INCLUDE_DIR:PATH=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/include

//Path to a library.
OSGSIM_LIBRARY_DEBUG:FILEPATH=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/debug/lib/osgSimd.lib

//Path to a library.
OSGSIM_LIBRARY_RELEASE:FILEPATH=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/lib/osgSim.lib

//Path to a file.
OSGTEXT_INCLUDE_DIR:PATH=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/include

//Path to a library.
OSGTEXT_LIBRARY_DEBUG:FILEPATH=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/debug/lib/osgTextd.lib

//Path to a library.
OSGTEXT_LIBRARY_RELEASE:FILEPATH=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/lib/osgText.lib

//Path to a file.
OSGUTIL_INCLUDE_DIR:PATH=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/include

//Path to a library.
OSGUTIL_LIBRARY_DEBUG:FILEPATH=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/debug/lib/osgUtild.lib

//Path to a library.
OSGUTIL_LIBRARY_RELEASE:FILEPATH=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/lib/osgUtil.lib

//Path to a file.
OSGVIEWER_INCLUDE_DIR:PATH=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/include

//Path to a library.
OSGVIEWER_LIBRARY_DEBUG:FILEPATH=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/debug/lib/osgViewerd.lib

//Path to a library.
OSGVIEWER_LIBRARY_RELEASE:FILEPATH=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/lib/osgViewer.lib

//Path to a file.
OSG_INCLUDE_DIR:PATH=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/include

//Path to a library.
OSG_LIBRARY_DEBUG:FILEPATH=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/debug/lib/osgd.lib

//Path to a library.
OSG_LIBRARY_RELEASE:FILEPATH=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/lib/osg.lib

OSG_PLUGINS:STRING=osgPlugins-3.6.5

//Set this to true if Protobuf is compiled as dll
PROTOBUF_USE_DLLS:BOOL=FALSE

//The directory containing a CMake configuration file for Protobuf.
Protobuf_DIR:PATH=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/share/protobuf

//Path to a program.
Protobuf_PROTOC_EXECUTABLE:FILEPATH=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/tools/protobuf/protoc.exe

//The directory containing a CMake configuration file for RocksDB.
RocksDB_DIR:PATH=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/share/rocksdb

//Path to a file.
SQLite3_INCLUDE_DIR:PATH=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/include

//Path to a library.
SQLite3_LIBRARY:FILEPATH=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/debug/lib/sqlite3.lib

//Automatically copy dependencies into the output directory for
// executables.
VCPKG_APPLOCAL_DEPS:BOOL=ON

//Additional options to bootstrap vcpkg
VCPKG_BOOTSTRAP_OPTIONS:STRING=

//The directory which contains the installed libraries for each
// triplet
VCPKG_INSTALLED_DIR:PATH=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed

//Additional install options to pass to vcpkg
VCPKG_INSTALL_OPTIONS:STRING=

//The path to the vcpkg manifest directory.
VCPKG_MANIFEST_DIR:PATH=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu

//Install the dependencies listed in your manifest:
//\n    If this is off, you will have to manually install your dependencies.
//\n    See https://github.com/microsoft/vcpkg/tree/master/docs/specifications/manifests.md
// for more info.
//\n
VCPKG_MANIFEST_INSTALL:BOOL=ON

//Use manifest mode, as opposed to classic mode.
VCPKG_MANIFEST_MODE:BOOL=ON

//Overlay ports to use for vcpkg install in manifest mode
VCPKG_OVERLAY_PORTS:STRING=

//Overlay triplets to use for vcpkg install in manifest mode
VCPKG_OVERLAY_TRIPLETS:STRING=

//Appends the vcpkg paths to CMAKE_PREFIX_PATH, CMAKE_LIBRARY_PATH
// and CMAKE_FIND_ROOT_PATH so that vcpkg libraries/packages are
// found after toolchain/system libraries/packages.
VCPKG_PREFER_SYSTEM_LIBS:BOOL=OFF

//Enable the setup of CMAKE_PROGRAM_PATH to vcpkg paths
VCPKG_SETUP_CMAKE_PROGRAM_PATH:BOOL=ON

//Vcpkg target triplet (ex. x86-windows)
VCPKG_TARGET_TRIPLET:STRING=x64-windows

//Trace calls to find_package()
VCPKG_TRACE_FIND_PACKAGE:BOOL=OFF

//Enables messages from the VCPKG toolchain for debugging purposes.
VCPKG_VERBOSE:BOOL=OFF

//The directory containing a CMake configuration file for WebP.
WebP_DIR:PATH=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/share/WebP

//(experimental) Automatically copy dependencies into the install
// target directory for executables. Requires CMake 3.14.
X_VCPKG_APPLOCAL_DEPS_INSTALL:BOOL=OFF

//(experimental) Add USES_TERMINAL to VCPKG_APPLOCAL_DEPS to force
// serialization.
X_VCPKG_APPLOCAL_DEPS_SERIALIZED:BOOL=OFF

//Path to a file.
ZLIB_INCLUDE_DIR:PATH=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/include

//Path to a library.
ZLIB_LIBRARY_DEBUG:FILEPATH=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/debug/lib/zlibd.lib

//Path to a library.
ZLIB_LIBRARY_RELEASE:FILEPATH=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/lib/zlib.lib

//Path to a program.
Z_VCPKG_PWSH_PATH:FILEPATH=C:/Program Files/PowerShell/7/pwsh.exe

//The directory which contains the installed libraries for each
// triplet
_VCPKG_INSTALLED_DIR:PATH=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed

//Path to a library.
_vcpkg_libwebp_LIBRARY_DEBUG:FILEPATH=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/debug/lib/libwebp.lib

//Path to a library.
_vcpkg_libwebp_LIBRARY_RELEASE:FILEPATH=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/lib/libwebp.lib

//Path to a library.
_vcpkg_libwebpdecoder_LIBRARY_DEBUG:FILEPATH=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/debug/lib/libwebpdecoder.lib

//Path to a library.
_vcpkg_libwebpdecoder_LIBRARY_RELEASE:FILEPATH=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/lib/libwebpdecoder.lib

//Path to a library.
_vcpkg_libwebpdemux_LIBRARY_DEBUG:FILEPATH=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/debug/lib/libwebpdemux.lib

//Path to a library.
_vcpkg_libwebpdemux_LIBRARY_RELEASE:FILEPATH=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/lib/libwebpdemux.lib

//Path to a library.
_vcpkg_libwebpmux_LIBRARY_DEBUG:FILEPATH=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/debug/lib/libwebpmux.lib

//Path to a library.
_vcpkg_libwebpmux_LIBRARY_RELEASE:FILEPATH=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/lib/libwebpmux.lib

//Path to a library.
_vcpkg_sharpyuv_LIBRARY_DEBUG:FILEPATH=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/debug/lib/libsharpyuv.lib

//Path to a library.
_vcpkg_sharpyuv_LIBRARY_RELEASE:FILEPATH=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/lib/libsharpyuv.lib

//The directory containing a CMake configuration file for absl.
absl_DIR:PATH=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/share/absl

//The directory containing a CMake configuration file for blend2d.
blend2d_DIR:PATH=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/share/blend2d

//The directory containing a CMake configuration file for blosc.
blosc_DIR:PATH=blosc_DIR-NOTFOUND

//The directory containing a CMake configuration file for fmt.
fmt_DIR:PATH=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/share/fmt

//The directory containing a CMake configuration file for geographiclib.
geographiclib_DIR:PATH=geographiclib_DIR-NOTFOUND

//The directory containing a CMake configuration file for geos.
geos_DIR:PATH=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/share/geos

//The directory containing a CMake configuration file for libzip.
libzip_DIR:PATH=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/share/libzip

//The directory containing a CMake configuration file for meshoptimizer.
meshoptimizer_DIR:PATH=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/share/meshoptimizer

//CMake built-in FindProtobuf.cmake module compatible
protobuf_MODULE_COMPATIBLE:BOOL=OFF

//Enable for verbose output
protobuf_VERBOSE:BOOL=OFF

//The directory containing a CMake configuration file for spdlog.
spdlog_DIR:PATH=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/share/spdlog

//The directory containing a CMake configuration file for utf8_range.
utf8_range_DIR:PATH=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/share/utf8_range


########################
# INTERNAL cache entries
########################

//ADVANCED property for variable: CMAKE_AR
CMAKE_AR-ADVANCED:INTERNAL=1
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=f:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=3
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=26
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=4
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=C:/Program Files/CMake/bin/cmake.exe
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=C:/Program Files/CMake/bin/cpack.exe
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=C:/Program Files/CMake/bin/ctest.exe
//ADVANCED property for variable: CMAKE_CXX_FLAGS
CMAKE_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_DEBUG
CMAKE_CXX_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_MINSIZEREL
CMAKE_CXX_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELEASE
CMAKE_CXX_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELWITHDEBINFO
CMAKE_CXX_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_STANDARD_LIBRARIES
CMAKE_CXX_STANDARD_LIBRARIES-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS
CMAKE_C_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_DEBUG
CMAKE_C_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_MINSIZEREL
CMAKE_C_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELEASE
CMAKE_C_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELWITHDEBINFO
CMAKE_C_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_STANDARD_LIBRARIES
CMAKE_C_STANDARD_LIBRARIES-ADVANCED:INTERNAL=1
//Executable file format
CMAKE_EXECUTABLE_FORMAT:INTERNAL=Unknown
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS
CMAKE_EXE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_DEBUG
CMAKE_EXE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_MINSIZEREL
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELEASE
CMAKE_EXE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Visual Studio 17 2022
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=C:/Program Files/Microsoft Visual Studio/2022/Enterprise
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Test CMAKE_HAVE_LIBC_PTHREAD
CMAKE_HAVE_LIBC_PTHREAD:INTERNAL=
//Have library pthreads
CMAKE_HAVE_PTHREADS_CREATE:INTERNAL=
//Have library pthread
CMAKE_HAVE_PTHREAD_CREATE:INTERNAL=
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu
//ADVANCED property for variable: CMAKE_INSTALL_BINDIR
CMAKE_INSTALL_BINDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DATADIR
CMAKE_INSTALL_DATADIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DATAROOTDIR
CMAKE_INSTALL_DATAROOTDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DOCDIR
CMAKE_INSTALL_DOCDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_INCLUDEDIR
CMAKE_INSTALL_INCLUDEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_INFODIR
CMAKE_INSTALL_INFODIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LIBDIR
CMAKE_INSTALL_LIBDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LIBEXECDIR
CMAKE_INSTALL_LIBEXECDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LOCALEDIR
CMAKE_INSTALL_LOCALEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LOCALSTATEDIR
CMAKE_INSTALL_LOCALSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_MANDIR
CMAKE_INSTALL_MANDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_OLDINCLUDEDIR
CMAKE_INSTALL_OLDINCLUDEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_RUNSTATEDIR
CMAKE_INSTALL_RUNSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SBINDIR
CMAKE_INSTALL_SBINDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SHAREDSTATEDIR
CMAKE_INSTALL_SHAREDSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SYSCONFDIR
CMAKE_INSTALL_SYSCONFDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_LINKER
CMAKE_LINKER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS
CMAKE_MODULE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_DEBUG
CMAKE_MODULE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELEASE
CMAKE_MODULE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MT
CMAKE_MT-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=55
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//noop for ranlib
CMAKE_RANLIB:INTERNAL=:
//ADVANCED property for variable: CMAKE_RC_COMPILER
CMAKE_RC_COMPILER-ADVANCED:INTERNAL=1
CMAKE_RC_COMPILER_WORKS:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS
CMAKE_RC_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_DEBUG
CMAKE_RC_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_MINSIZEREL
CMAKE_RC_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_RELEASE
CMAKE_RC_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_RELWITHDEBINFO
CMAKE_RC_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=C:/Program Files/CMake/share/cmake-3.26
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS
CMAKE_SHARED_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_DEBUG
CMAKE_SHARED_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELEASE
CMAKE_SHARED_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS
CMAKE_STATIC_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_DEBUG
CMAKE_STATIC_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELEASE
CMAKE_STATIC_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_TOOLCHAIN_FILE
CMAKE_TOOLCHAIN_FILE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CURL_LIBRARY_DEBUG
CURL_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//vcpkg
CURL_LIBRARY_DEBUG:INTERNAL=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/debug/lib/libcurl-d.lib
//ADVANCED property for variable: CURL_LIBRARY_RELEASE
CURL_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//vcpkg
CURL_LIBRARY_RELEASE:INTERNAL=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/lib/libcurl.lib
//Details about finding OpenGL
FIND_PACKAGE_MESSAGE_DETAILS_OpenGL:INTERNAL=[opengl32][c ][v()]
//Details about finding OpenSceneGraph
FIND_PACKAGE_MESSAGE_DETAILS_OpenSceneGraph:INTERNAL=[optimized;F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/lib/osgManipulator.lib;debug;F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/debug/lib/osgManipulatord.lib;optimized;F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/lib/osgShadow.lib;debug;F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/debug/lib/osgShadowd.lib;optimized;F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/lib/osgSim.lib;debug;F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/debug/lib/osgSimd.lib;optimized;F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/lib/osgViewer.lib;debug;F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/debug/lib/osgViewerd.lib;optimized;F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/lib/osgGA.lib;debug;F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/debug/lib/osgGAd.lib;optimized;F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/lib/osgUtil.lib;debug;F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/debug/lib/osgUtild.lib;optimized;F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/lib/osgText.lib;debug;F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/debug/lib/osgTextd.lib;optimized;F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/lib/osgDB.lib;debug;F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/debug/lib/osgDBd.lib;optimized;F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/lib/osg.lib;debug;F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/debug/lib/osgd.lib;optimized;F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/lib/OpenThreads.lib;debug;F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/debug/lib/OpenThreadsd.lib][F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/include][TRUE][TRUE][TRUE][TRUE][TRUE][TRUE][TRUE][TRUE][TRUE][TRUE][v3.6.5()]
//Details about finding OpenThreads
FIND_PACKAGE_MESSAGE_DETAILS_OpenThreads:INTERNAL=[optimized;F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/lib/OpenThreads.lib;debug;F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/debug/lib/OpenThreadsd.lib][F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/include][v()]
//Details about finding SQLite3
FIND_PACKAGE_MESSAGE_DETAILS_SQLite3:INTERNAL=[F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/include][F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/debug/lib/sqlite3.lib][v3.50.2()]
//Details about finding Threads
FIND_PACKAGE_MESSAGE_DETAILS_Threads:INTERNAL=[TRUE][v()]
//Details about finding ZLIB
FIND_PACKAGE_MESSAGE_DETAILS_ZLIB:INTERNAL=[optimized;F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/lib/zlib.lib;debug;F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/debug/lib/zlibd.lib][F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/include][c ][v1.3.1(1)]
//Details about finding osg
FIND_PACKAGE_MESSAGE_DETAILS_osg:INTERNAL=[optimized;F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/lib/osg.lib;debug;F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/debug/lib/osgd.lib][F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/include][v()]
//Details about finding osgDB
FIND_PACKAGE_MESSAGE_DETAILS_osgDB:INTERNAL=[optimized;F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/lib/osgDB.lib;debug;F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/debug/lib/osgDBd.lib][F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/include][v()]
//Details about finding osgGA
FIND_PACKAGE_MESSAGE_DETAILS_osgGA:INTERNAL=[optimized;F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/lib/osgGA.lib;debug;F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/debug/lib/osgGAd.lib][F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/include][v()]
//Details about finding osgManipulator
FIND_PACKAGE_MESSAGE_DETAILS_osgManipulator:INTERNAL=[optimized;F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/lib/osgManipulator.lib;debug;F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/debug/lib/osgManipulatord.lib][F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/include][v()]
//Details about finding osgShadow
FIND_PACKAGE_MESSAGE_DETAILS_osgShadow:INTERNAL=[optimized;F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/lib/osgShadow.lib;debug;F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/debug/lib/osgShadowd.lib][F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/include][v()]
//Details about finding osgSim
FIND_PACKAGE_MESSAGE_DETAILS_osgSim:INTERNAL=[optimized;F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/lib/osgSim.lib;debug;F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/debug/lib/osgSimd.lib][F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/include][v()]
//Details about finding osgText
FIND_PACKAGE_MESSAGE_DETAILS_osgText:INTERNAL=[optimized;F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/lib/osgText.lib;debug;F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/debug/lib/osgTextd.lib][F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/include][v()]
//Details about finding osgUtil
FIND_PACKAGE_MESSAGE_DETAILS_osgUtil:INTERNAL=[optimized;F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/lib/osgUtil.lib;debug;F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/debug/lib/osgUtild.lib][F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/include][v()]
//Details about finding osgViewer
FIND_PACKAGE_MESSAGE_DETAILS_osgViewer:INTERNAL=[optimized;F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/lib/osgViewer.lib;debug;F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/debug/lib/osgViewerd.lib][F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/include][v()]
GDAL_INCLUDE_DIR:INTERNAL=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/include
GDAL_LIBRARY:INTERNAL=GDAL::GDAL
//ADVANCED property for variable: GIT_EXECUTABLE
GIT_EXECUTABLE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENGL_gl_LIBRARY
OPENGL_gl_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENGL_glu_LIBRARY
OPENGL_glu_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENSCENEGRAPH_VERSION
OPENSCENEGRAPH_VERSION-ADVANCED:INTERNAL=1
//The version of OSG which was detected
OPENSCENEGRAPH_VERSION:INTERNAL=3.6.5
//ADVANCED property for variable: OPENTHREADS_LIBRARY_DEBUG
OPENTHREADS_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENTHREADS_LIBRARY_RELEASE
OPENTHREADS_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSGDB_LIBRARY_DEBUG
OSGDB_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSGDB_LIBRARY_RELEASE
OSGDB_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSGEARTH_ASSUME_SINGLE_GL_CONTEXT
OSGEARTH_ASSUME_SINGLE_GL_CONTEXT-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSGEARTH_ASSUME_SINGLE_THREADED_OSG
OSGEARTH_ASSUME_SINGLE_THREADED_OSG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSGEARTH_BUILD_DOCS
OSGEARTH_BUILD_DOCS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSGEARTH_BUILD_LEGACY_CONTROLS_API
OSGEARTH_BUILD_LEGACY_CONTROLS_API-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSGEARTH_BUILD_LEGACY_SPLAT_NODEKIT
OSGEARTH_BUILD_LEGACY_SPLAT_NODEKIT-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSGEARTH_BUILD_PROCEDURAL_NODEKIT
OSGEARTH_BUILD_PROCEDURAL_NODEKIT-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSGEARTH_BUILD_TESTS
OSGEARTH_BUILD_TESTS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSGEARTH_BUILD_ZIP_PLUGIN
OSGEARTH_BUILD_ZIP_PLUGIN-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSGEARTH_ENABLE_PROFILING
OSGEARTH_ENABLE_PROFILING-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSGEARTH_ENABLE_WININET_FOR_HTTP
OSGEARTH_ENABLE_WININET_FOR_HTTP-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSGEARTH_GLCORE_INCLUDE_DIR
OSGEARTH_GLCORE_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSGEARTH_INSTALL_SHADERS
OSGEARTH_INSTALL_SHADERS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSGGA_LIBRARY_DEBUG
OSGGA_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSGGA_LIBRARY_RELEASE
OSGGA_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSGMANIPULATOR_LIBRARY_DEBUG
OSGMANIPULATOR_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSGMANIPULATOR_LIBRARY_RELEASE
OSGMANIPULATOR_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSGSHADOW_LIBRARY_DEBUG
OSGSHADOW_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSGSHADOW_LIBRARY_RELEASE
OSGSHADOW_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSGSIM_LIBRARY_DEBUG
OSGSIM_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSGSIM_LIBRARY_RELEASE
OSGSIM_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSGTEXT_LIBRARY_DEBUG
OSGTEXT_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSGTEXT_LIBRARY_RELEASE
OSGTEXT_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSGUTIL_LIBRARY_DEBUG
OSGUTIL_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSGUTIL_LIBRARY_RELEASE
OSGUTIL_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSGVIEWER_LIBRARY_DEBUG
OSGVIEWER_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSGVIEWER_LIBRARY_RELEASE
OSGVIEWER_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSG_LIBRARY_DEBUG
OSG_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSG_LIBRARY_RELEASE
OSG_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSG_PLUGINS
OSG_PLUGINS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: SQLite3_INCLUDE_DIR
SQLite3_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: SQLite3_LIBRARY
SQLite3_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: VCPKG_VERBOSE
VCPKG_VERBOSE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: ZLIB_INCLUDE_DIR
ZLIB_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: ZLIB_LIBRARY_DEBUG
ZLIB_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: ZLIB_LIBRARY_RELEASE
ZLIB_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//Making sure VCPKG_MANIFEST_MODE doesn't change
Z_VCPKG_CHECK_MANIFEST_MODE:INTERNAL=ON
//The path to the PowerShell implementation to use.
Z_VCPKG_POWERSHELL_PATH:INTERNAL=C:/Program Files/PowerShell/7/pwsh.exe
//Vcpkg root directory
Z_VCPKG_ROOT_DIR:INTERNAL=C:/dev/vcpkg
//CMAKE_INSTALL_PREFIX during last run
_GNUInstallDirs_LAST_CMAKE_INSTALL_PREFIX:INTERNAL=C:/Program Files (x86)/OSGEARTH
//ADVANCED property for variable: _vcpkg_libwebp_LIBRARY_DEBUG
_vcpkg_libwebp_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: _vcpkg_libwebp_LIBRARY_RELEASE
_vcpkg_libwebp_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: _vcpkg_libwebpdecoder_LIBRARY_DEBUG
_vcpkg_libwebpdecoder_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: _vcpkg_libwebpdecoder_LIBRARY_RELEASE
_vcpkg_libwebpdecoder_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: _vcpkg_libwebpdemux_LIBRARY_DEBUG
_vcpkg_libwebpdemux_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: _vcpkg_libwebpdemux_LIBRARY_RELEASE
_vcpkg_libwebpdemux_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: _vcpkg_libwebpmux_LIBRARY_DEBUG
_vcpkg_libwebpmux_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: _vcpkg_libwebpmux_LIBRARY_RELEASE
_vcpkg_libwebpmux_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: _vcpkg_sharpyuv_LIBRARY_DEBUG
_vcpkg_sharpyuv_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: _vcpkg_sharpyuv_LIBRARY_RELEASE
_vcpkg_sharpyuv_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: protobuf_MODULE_COMPATIBLE
protobuf_MODULE_COMPATIBLE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: protobuf_VERBOSE
protobuf_VERBOSE-ADVANCED:INTERNAL=1

