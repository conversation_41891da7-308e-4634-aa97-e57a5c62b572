/* -*-c++-*- */
/* osgEarth - Geospatial SDK for OpenSceneGraph
 * Copyright 2025 Pelican Mapping
 * http://osgearth.org
 *
 * Simplified OGRFeatureSource implementation without OGR dependencies
 */
#include <osgEarth/OGRFeatureSource_Simplified>
#include <osgEarth/Notify>

using namespace osgEarth;

#define LC "[OGRFeatureSource] "

REGISTER_OSGEARTH_LAYER(ogrfeatures, OGRFeatureSource);

//........................................................................

Config OGRFeatureSource::Options::getConfig() const
{
    Config conf = FeatureSource::Options::getConfig();
    conf.set("url", _url);
    conf.set("ogr_driver", _ogrDriver);
    conf.set("geometry_type", _geometryTypeOverride);
    conf.set("open_write", _openWrite);
    conf.set("build_spatial_index", _buildSpatialIndex);
    conf.set("force_rebuild_spatial_index", _forceRebuildSpatialIndex);
    return conf;
}

void OGRFeatureSource::Options::fromConfig(const Config& conf)
{
    conf.get("url", _url);
    conf.get("ogr_driver", _ogrDriver);
    conf.get("geometry_type", _geometryTypeOverride);
    conf.get("open_write", _openWrite);
    conf.get("build_spatial_index", _buildSpatialIndex);
    conf.get("force_rebuild_spatial_index", _forceRebuildSpatialIndex);
}

//........................................................................

OGRFeatureSource::~OGRFeatureSource()
{
    // No cleanup needed
}

void OGRFeatureSource::init()
{
    FeatureSource::init();
    OE_WARN << LC << "OGRFeatureSource not supported in simplified build" << std::endl;
}

void OGRFeatureSource::setURL(const URI& value)
{
    options().url() = value;
}

const URI& OGRFeatureSource::getURL() const
{
    return options().url().get();
}

void OGRFeatureSource::setOGRDriver(const std::string& value)
{
    options().ogrDriver() = value;
}

const std::string& OGRFeatureSource::getOGRDriver() const
{
    return options().ogrDriver().get();
}

void OGRFeatureSource::setBuildSpatialIndex(const bool& value)
{
    options().buildSpatialIndex() = value;
}

const bool& OGRFeatureSource::getBuildSpatialIndex() const
{
    return options().buildSpatialIndex().get();
}

Status OGRFeatureSource::openImplementation()
{
    return Status::Error("OGRFeatureSource not supported in simplified build");
}

Status OGRFeatureSource::closeImplementation()
{
    return Status::OK();
}

FeatureCursor* OGRFeatureSource::createFeatureCursorImplementation(const Query& query, ProgressCallback* progress) const
{
    return nullptr;
}

bool OGRFeatureSource::deleteFeature(FeatureID fid)
{
    return false;
}

int OGRFeatureSource::getFeatureCount() const
{
    return 0;
}

bool OGRFeatureSource::supportsGetFeature() const
{
    return false;
}

Feature* OGRFeatureSource::getFeature(FeatureID fid)
{
    return nullptr;
}

bool OGRFeatureSource::isWritable() const
{
    return false;
}

const FeatureSchema& OGRFeatureSource::getSchema() const
{
    return _schema;
}

bool OGRFeatureSource::insertFeature(Feature* feature)
{
    return false;
}

Geometry::Type OGRFeatureSource::getGeometryType() const
{
    return Geometry::TYPE_UNKNOWN;
}

const Status& OGRFeatureSource::create(const FeatureProfile* profile, const FeatureSchema& schema, const Geometry::Type& geometryType, const osgDB::Options* readOptions)
{
    _createStatus = Status::Error("OGRFeatureSource creation not supported in simplified build");
    return _createStatus;
}

void OGRFeatureSource::buildSpatialIndex()
{
    // No-op
}

void OGRFeatureSource::dirty()
{
    // No-op
}
