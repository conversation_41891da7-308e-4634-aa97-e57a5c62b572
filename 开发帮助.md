# osgEarth简化版开发帮助文档

## 概述

osgEarth简化版是一个轻量级的地理空间渲染库，专门为支持XYZ瓦片和AWS Terrarium高程数据而设计。本版本移除了对GDAL和PROJ库的依赖，大幅简化了项目结构和部署复杂度。

## 主要功能

### 支持的功能
- ✅ XYZ瓦片地图显示（谷歌地图、OpenStreetMap等）
- ✅ AWS Terrarium高程数据
- ✅ 基本坐标系统转换（WGS84、Web Mercator、Geocentric）
- ✅ 地图缓存和性能优化
- ✅ 多线程瓦片下载
- ✅ OSG场景图集成

### 不支持的功能
- ❌ 复杂地理数据格式（Shapefile、GeoTIFF等）
- ❌ 高精度坐标转换
- ❌ 复杂空间分析
- ❌ 要素数据读写
- ❌ 自定义投影

## 快速开始

### 1. 基本地图显示

```cpp
#include <osgEarth/Map>
#include <osgEarth/MapNode>
#include <osgEarth/XYZ>
#include <osgViewer/Viewer>

int main()
{
    // 创建地图
    osg::ref_ptr<osgEarth::Map> map = new osgEarth::Map();
    
    // 添加XYZ瓦片图层
    osgEarth::XYZImageLayer::Options xyzOptions;
    xyzOptions.url() = "https://tile.openstreetmap.org/{z}/{x}/{y}.png";
    xyzOptions.profile() = osgEarth::Registry::instance()->getGlobalGeodeticProfile();
    
    osg::ref_ptr<osgEarth::XYZImageLayer> layer = new osgEarth::XYZImageLayer(xyzOptions);
    map->addLayer(layer);
    
    // 创建地图节点
    osg::ref_ptr<osgEarth::MapNode> mapNode = new osgEarth::MapNode(map);
    
    // 创建查看器
    osgViewer::Viewer viewer;
    viewer.setSceneData(mapNode);
    
    return viewer.run();
}
```

### 2. 添加高程数据

```cpp
#include <osgEarth/AWSTerrarium>

// 添加AWS Terrarium高程图层
osgEarth::AWSTerrarium::Options elevOptions;
elevOptions.url() = "https://s3.amazonaws.com/elevation-tiles-prod/terrarium/{z}/{x}/{y}.png";

osg::ref_ptr<osgEarth::AWSTerrarium> elevLayer = new osgEarth::AWSTerrarium(elevOptions);
map->addLayer(elevLayer);
```

### 3. 坐标转换

```cpp
#include <osgEarth/SpatialReference>

// 创建坐标系统
osg::ref_ptr<const osgEarth::SpatialReference> wgs84 = 
    osgEarth::SpatialReference::create("wgs84");
osg::ref_ptr<const osgEarth::SpatialReference> webMercator = 
    osgEarth::SpatialReference::create("spherical-mercator");

// 坐标转换
osg::Vec3d input(116.3974, 39.9093, 0); // 北京坐标 (经度, 纬度, 高度)
osg::Vec3d output;

if (wgs84->transform(input, webMercator, output))
{
    std::cout << "Web Mercator坐标: " << output.x() << ", " << output.y() << std::endl;
}
```

### 4. 自定义XYZ服务

```cpp
// 谷歌地图瓦片
osgEarth::XYZImageLayer::Options googleOptions;
googleOptions.url() = "https://mt1.google.com/vt/lyrs=s&x={x}&y={y}&z={z}";
googleOptions.profile() = osgEarth::Registry::instance()->getSphericalMercatorProfile();

// Bing地图瓦片
osgEarth::XYZImageLayer::Options bingOptions;
bingOptions.url() = "https://ecn.t0.tiles.virtualearth.net/tiles/a{quadkey}.jpeg?g=1";
bingOptions.profile() = osgEarth::Registry::instance()->getSphericalMercatorProfile();
```

## API参考

### 核心类

#### SpatialReference
坐标系统管理类，支持基本的坐标转换。

```cpp
class SpatialReference
{
public:
    // 创建坐标系统
    static SpatialReference* create(const std::string& init);
    
    // 坐标转换
    bool transform(const osg::Vec3d& input, 
                   const SpatialReference* outputSRS, 
                   osg::Vec3d& output) const;
    
    // 坐标系统类型判断
    bool isGeographic() const;
    bool isProjected() const;
    bool isGeocentric() const;
    bool isSphericalMercator() const;
    
    // 获取相关坐标系统
    const SpatialReference* getGeographicSRS() const;
    const SpatialReference* getGeocentricSRS() const;
};
```

#### XYZImageLayer
XYZ瓦片图层类。

```cpp
class XYZImageLayer : public ImageLayer
{
public:
    struct Options : public ImageLayer::Options
    {
        OC_OPTION(URI, url);           // 瓦片URL模板
        OC_OPTION(Profile*, profile);  // 瓦片配置文件
        OC_OPTION(bool, invertY);      // 是否反转Y坐标
    };
    
    // 设置瓦片URL
    void setURL(const URI& url);
    const URI& getURL() const;
};
```

#### Map
地图容器类，管理所有图层。

```cpp
class Map : public osg::Referenced
{
public:
    // 图层管理
    void addLayer(Layer* layer);
    void removeLayer(Layer* layer);
    LayerVector getLayers() const;
    
    // 坐标系统
    const SpatialReference* getSRS() const;
    const Profile* getProfile() const;
};
```

### 工具类

#### ProjectionUtils
坐标投影工具类。

```cpp
class ProjectionUtils
{
public:
    // WGS84与Web Mercator转换
    static bool wgs84ToWebMercator(double lon, double lat, double& x, double& y);
    static bool webMercatorToWgs84(double x, double y, double& lon, double& lat);
    
    // 地理坐标与地心坐标转换
    static bool geodeticToGeocentric(double lon, double lat, double alt, 
                                     double& x, double& y, double& z);
    static bool geocentricToGeodetic(double x, double y, double z, 
                                     double& lon, double& lat, double& alt);
};
```

## 配置选项

### 瓦片缓存配置

```cpp
// 设置缓存大小（字节）
osgEarth::Registry::instance()->setMaxCacheSize(100 * 1024 * 1024); // 100MB

// 设置缓存目录
osgEarth::Registry::instance()->setCacheDirectory("./cache");
```

### 网络配置

```cpp
// 设置HTTP超时时间
osgEarth::HTTPClient::setDefaultTimeout(30); // 30秒

// 设置并发连接数
osgEarth::HTTPClient::setMaxConcurrentRequests(8);
```

### 性能配置

```cpp
// 设置最大纹理尺寸
osgEarth::Registry::instance()->setMaxTextureSize(2048);

// 设置LOD范围
layer->setMinLevel(0);
layer->setMaxLevel(18);
```

## 常见问题

### Q: 如何添加自定义的XYZ瓦片服务？
A: 创建XYZImageLayer并设置正确的URL模板和配置文件：

```cpp
osgEarth::XYZImageLayer::Options options;
options.url() = "https://your-tile-server.com/{z}/{x}/{y}.png";
options.profile() = osgEarth::Registry::instance()->getSphericalMercatorProfile();
```

### Q: 坐标转换不准确怎么办？
A: 简化版只支持基本的坐标转换，精度有限。对于高精度需求，建议使用完整版osgEarth。

### Q: 如何处理网络连接问题？
A: 设置合适的超时时间和重试机制：

```cpp
osgEarth::HTTPClient::setDefaultTimeout(60);
osgEarth::HTTPClient::setRetryCount(3);
```

### Q: 内存使用过多怎么办？
A: 调整缓存大小和纹理尺寸：

```cpp
osgEarth::Registry::instance()->setMaxCacheSize(50 * 1024 * 1024); // 50MB
osgEarth::Registry::instance()->setMaxTextureSize(1024);
```

## 性能优化建议

1. **合理设置缓存大小**：根据可用内存调整缓存大小
2. **限制LOD级别**：避免加载过高精度的瓦片
3. **使用合适的瓦片服务**：选择响应速度快的瓦片服务
4. **预加载关键区域**：对重要区域进行预加载
5. **优化网络设置**：调整并发连接数和超时时间

## 部署说明

### 依赖库
- OpenSceneGraph 3.6+
- GEOS 3.13+
- libcurl
- 标准C++17编译器

### 编译选项
```cmake
set(CMAKE_CXX_STANDARD 17)
find_package(OpenSceneGraph REQUIRED)
find_package(GEOS REQUIRED)
find_package(CURL REQUIRED)
```

### 运行时要求
- 稳定的网络连接（用于下载瓦片）
- 足够的磁盘空间（用于缓存）
- 支持OpenGL的图形卡

## osgearth_myviewer应用程序

### 编译和运行

#### 自动化构建
```bash
# 运行自动化构建脚本
build_and_deploy.bat
```

#### 手动构建
```bash
# 1. 配置CMake
mkdir build_desk
cd build_desk
cmake .. -DCMAKE_BUILD_TYPE=Release -DCMAKE_TOOLCHAIN_FILE=C:/dev/vcpkg/scripts/buildsystems/vcpkg.cmake

# 2. 编译osgEarth库
cmake --build . --target osgEarth --config Release

# 3. 编译osgearth_myviewer
cmake --build . --target osgearth_myviewer --config Release

# 4. 发布到redist_desk
deploy_to_redist.bat
```

#### 运行应用程序
```bash
# 方式1: 使用启动脚本
redist_desk\run_myviewer.bat

# 方式2: 直接运行
redist_desk\bin\osgearth_myviewer.exe

# 方式3: 显示帮助信息
redist_desk\bin\osgearth_myviewer.exe --help
```

### 应用程序特性

#### 数据源
- **谷歌卫星影像**: 高分辨率卫星图像
- **AWS Terrarium高程**: 全球高程数据
- **自动缓存**: 本地瓦片缓存提高性能

#### 交互功能
- **鼠标操作**:
  - 左键拖拽: 旋转地球
  - 右键拖拽: 缩放视图
  - 中键拖拽: 平移视图
- **键盘快捷键**:
  - 's': 显示/隐藏统计信息
  - 'w': 切换线框模式
- **实时坐标**: 鼠标移动时显示当前坐标

#### 渲染特性
- **对数深度缓冲**: 改善远距离渲染
- **自动裁剪**: 优化渲染性能
- **多线程加载**: 并发下载瓦片数据

### 目录结构

发布后的目录结构：
```
redist_desk/
├── bin/                    # 可执行文件和DLL
│   ├── osgearth_myviewer.exe
│   ├── osgEarth.dll
│   └── 其他依赖DLL
├── lib/                    # 静态库文件
│   └── osgEarth.lib
├── data/                   # 数据文件
├── config/                 # 配置文件
├── run_myviewer.bat       # 启动脚本
└── README.txt             # 使用说明
```

## 示例项目

### osgearth_myviewer源码结构
```cpp
// 主要组件
class CoordinateDisplayHandler;  // 坐标显示处理器
osg::ref_ptr<Map> createMap();   // 地图创建函数
void addAtmosphericEffects();    // 大气效果（简化版）
void configureViewer();          // 查看器配置
```

### 自定义数据源示例
```cpp
// 添加自定义XYZ瓦片服务
XYZImageLayer::Options options;
options.url() = "https://your-tile-server.com/{z}/{x}/{y}.png";
options.name() = "Custom Tiles";

osg::ref_ptr<XYZImageLayer> layer = new XYZImageLayer(options);
layer->setProfile(Registry::instance()->getSphericalMercatorProfile());
map->addLayer(layer);
```

## 技术支持

### 故障排除

1. **编译错误**:
   - 确保vcpkg已正确安装
   - 检查CMake工具链文件路径
   - 验证依赖库版本

2. **运行时错误**:
   - 检查DLL依赖是否完整
   - 确保网络连接正常
   - 查看控制台错误信息

3. **性能问题**:
   - 调整缓存大小设置
   - 检查网络带宽
   - 优化LOD级别设置

### 参考资源

1. **技术架构设计文档**: 详细的系统架构说明
2. **API文档**: 完整的接口参考
3. **源代码**: osgearth_myviewer.cpp示例实现
4. **构建脚本**: 自动化构建和发布流程

### 扩展开发

对于更复杂的地理空间需求，可以考虑：
- 集成更多数据源类型
- 添加矢量数据支持
- 实现高级渲染效果
- 开发自定义用户界面

对于企业级应用，建议使用完整版osgEarth或其他专业GIS库。
