/* -*-c++-*- */
/* osgEarth - Geospatial SDK for OpenSceneGraph
 * Copyright 2025 Pelican Mapping
 * http://osgearth.org
 *
 * Simplified OgrUtils implementation without OGR dependencies
 */
#include <osgEarth/OgrUtils>
#include <osgEarth/Notify>

#define LC "[OgrUtils] "

using namespace osgEarth;
using namespace osgEarth::Util;

// Simplified OgrUtils without OGR dependencies

void OgrUtils::populate(void* geomHandle, Geometry* target, int numPoints)
{
    // No-op in simplified build
    OE_WARN << LC << "OgrUtils::populate not supported in simplified build" << std::endl;
}

MultiGeometry* OgrUtils::createTIN(void* geomHandle)
{
    OE_WARN << LC << "OgrUtils::createTIN not supported in simplified build" << std::endl;
    return nullptr;
}

Polygon* OgrUtils::createPolygon(void* geomHandle, bool rewindPolygons)
{
    OE_WARN << LC << "OgrUtils::createPolygon not supported in simplified build" << std::endl;
    return nullptr;
}

LineString* OgrUtils::createLineString(void* geomHandle)
{
    OE_WARN << LC << "OgrUtils::createLineString not supported in simplified build" << std::endl;
    return nullptr;
}

PointSet* OgrUtils::createPointSet(void* geomHandle)
{
    OE_WARN << LC << "OgrUtils::createPointSet not supported in simplified build" << std::endl;
    return nullptr;
}

Point* OgrUtils::createPoint(void* geomHandle)
{
    OE_WARN << LC << "OgrUtils::createPoint not supported in simplified build" << std::endl;
    return nullptr;
}

MultiGeometry* OgrUtils::createMultiGeometry(void* geomHandle)
{
    OE_WARN << LC << "OgrUtils::createMultiGeometry not supported in simplified build" << std::endl;
    return nullptr;
}

Geometry* OgrUtils::createGeometry(void* geomHandle)
{
    OE_WARN << LC << "OgrUtils::createGeometry not supported in simplified build" << std::endl;
    return nullptr;
}

Feature* OgrUtils::createFeature(void* featureHandle, const SpatialReference* srs, const optional<GeoInterpolation>& interpolation, bool fidsArePrimaryKeys)
{
    OE_WARN << LC << "OgrUtils::createFeature not supported in simplified build" << std::endl;
    return nullptr;
}

AttributeType OgrUtils::getAttributeType(int type)
{
    return ATTRTYPE_UNSPECIFIED;
}

void* OgrUtils::createOgrGeometry(const Geometry* input, int dimension)
{
    OE_WARN << LC << "OgrUtils::createOgrGeometry not supported in simplified build" << std::endl;
    return nullptr;
}

void* OgrUtils::encodeTileKey(const TileKey& key)
{
    OE_WARN << LC << "OgrUtils::encodeTileKey not supported in simplified build" << std::endl;
    return nullptr;
}

TileKey OgrUtils::decodeTileKey(void* geomHandle)
{
    OE_WARN << LC << "OgrUtils::decodeTileKey not supported in simplified build" << std::endl;
    return TileKey::INVALID;
}

bool OgrUtils::isPolygonCCW(void* geomHandle)
{
    return false;
}

void OgrUtils::reversePointOrder(void* geomHandle)
{
    // No-op
}

std::string OgrUtils::getDriverName(void* ogrDriverHandle)
{
    return "";
}

bool OgrUtils::supportsRandomWrite(void* ogrDriverHandle)
{
    return false;
}

bool OgrUtils::canOpenForWrite(void* ogrDriverHandle)
{
    return false;
}

void* OgrUtils::createDataSource(const std::string& path, void* ogrDriverHandle)
{
    return nullptr;
}

void* OgrUtils::openDataSource(const std::string& path, bool update)
{
    return nullptr;
}

void OgrUtils::closeDataSource(void* ds)
{
    // No-op
}

void* OgrUtils::getLayer(void* ds, const std::string& name)
{
    return nullptr;
}

void* OgrUtils::getLayerByIndex(void* ds, int index)
{
    return nullptr;
}

int OgrUtils::getLayerCount(void* ds)
{
    return 0;
}

std::string OgrUtils::getLayerName(void* layer)
{
    return "";
}

void* OgrUtils::createLayer(void* ds, const std::string& name, void* spatialRef, int wkbType, char** options)
{
    return nullptr;
}

bool OgrUtils::deleteLayer(void* ds, void* layer)
{
    return false;
}

void* OgrUtils::getLayerDefn(void* layer)
{
    return nullptr;
}

int OgrUtils::getFieldCount(void* layerDefn)
{
    return 0;
}

void* OgrUtils::getFieldDefn(void* layerDefn, int i)
{
    return nullptr;
}

std::string OgrUtils::getFieldName(void* fieldDefn)
{
    return "";
}

int OgrUtils::getFieldType(void* fieldDefn)
{
    return 0;
}

int OgrUtils::getFieldIndex(void* layerDefn, const std::string& name)
{
    return -1;
}

void* OgrUtils::createFieldDefn(const std::string& name, int type)
{
    return nullptr;
}

bool OgrUtils::createField(void* layer, void* fieldDefn)
{
    return false;
}

void OgrUtils::destroyFieldDefn(void* fieldDefn)
{
    // No-op
}

void* OgrUtils::getNextFeature(void* layer)
{
    return nullptr;
}

void OgrUtils::resetReading(void* layer)
{
    // No-op
}

void OgrUtils::destroyFeature(void* feature)
{
    // No-op
}

void* OgrUtils::getFeatureGeometry(void* feature)
{
    return nullptr;
}

long OgrUtils::getFeatureFID(void* feature)
{
    return -1;
}

void OgrUtils::setFeatureFID(void* feature, long fid)
{
    // No-op
}

std::string OgrUtils::getFieldAsString(void* feature, int i)
{
    return "";
}

int OgrUtils::getFieldAsInt(void* feature, int i)
{
    return 0;
}

double OgrUtils::getFieldAsDouble(void* feature, int i)
{
    return 0.0;
}

bool OgrUtils::isFieldSet(void* feature, int i)
{
    return false;
}

void OgrUtils::setField(void* feature, int i, const std::string& value)
{
    // No-op
}

void OgrUtils::setField(void* feature, int i, int value)
{
    // No-op
}

void OgrUtils::setField(void* feature, int i, double value)
{
    // No-op
}

void* OgrUtils::createFeature(void* layerDefn)
{
    return nullptr;
}

void OgrUtils::setFeatureGeometry(void* feature, void* geom)
{
    // No-op
}

bool OgrUtils::createFeature(void* layer, void* feature)
{
    return false;
}

int OgrUtils::getFeatureCount(void* layer)
{
    return 0;
}

void* OgrUtils::getFeature(void* layer, long fid)
{
    return nullptr;
}

bool OgrUtils::deleteFeature(void* layer, long fid)
{
    return false;
}

void OgrUtils::setSpatialFilter(void* layer, void* geom)
{
    // No-op
}

void OgrUtils::clearSpatialFilter(void* layer)
{
    // No-op
}

void OgrUtils::setAttributeFilter(void* layer, const std::string& filter)
{
    // No-op
}

void OgrUtils::clearAttributeFilter(void* layer)
{
    // No-op
}

void* OgrUtils::cloneGeometry(void* geom)
{
    return nullptr;
}

void OgrUtils::destroyGeometry(void* geom)
{
    // No-op
}

int OgrUtils::getGeometryType(void* geom)
{
    return 0;
}

int OgrUtils::getPointCount(void* geom)
{
    return 0;
}

void OgrUtils::getPoint(void* geom, int i, double* x, double* y, double* z)
{
    if (x) *x = 0.0;
    if (y) *y = 0.0;
    if (z) *z = 0.0;
}

int OgrUtils::getGeometryCount(void* geom)
{
    return 0;
}

void* OgrUtils::getGeometryRef(void* geom, int i)
{
    return nullptr;
}

void* OgrUtils::createGeometry(int wkbType)
{
    return nullptr;
}

void OgrUtils::addPoint(void* geom, double x, double y, double z)
{
    // No-op
}

void OgrUtils::addGeometry(void* geom, void* subGeom)
{
    // No-op
}

void OgrUtils::closeRings(void* geom)
{
    // No-op
}

bool OgrUtils::isEmpty(void* geom)
{
    return true;
}

bool OgrUtils::isValid(void* geom)
{
    return false;
}

void* OgrUtils::buffer(void* geom, double distance, int quadSegs)
{
    return nullptr;
}

void* OgrUtils::intersection(void* geom1, void* geom2)
{
    return nullptr;
}

void* OgrUtils::difference(void* geom1, void* geom2)
{
    return nullptr;
}

void* OgrUtils::unionGeometry(void* geom1, void* geom2)
{
    return nullptr;
}

void* OgrUtils::convexHull(void* geom)
{
    return nullptr;
}

void* OgrUtils::simplify(void* geom, double tolerance)
{
    return nullptr;
}

void* OgrUtils::simplifyPreserveTopology(void* geom, double tolerance)
{
    return nullptr;
}

double OgrUtils::getArea(void* geom)
{
    return 0.0;
}

double OgrUtils::getLength(void* geom)
{
    return 0.0;
}

bool OgrUtils::contains(void* geom1, void* geom2)
{
    return false;
}

bool OgrUtils::intersects(void* geom1, void* geom2)
{
    return false;
}

bool OgrUtils::touches(void* geom1, void* geom2)
{
    return false;
}

bool OgrUtils::crosses(void* geom1, void* geom2)
{
    return false;
}

bool OgrUtils::within(void* geom1, void* geom2)
{
    return false;
}

bool OgrUtils::overlaps(void* geom1, void* geom2)
{
    return false;
}

double OgrUtils::distance(void* geom1, void* geom2)
{
    return 0.0;
}

void* OgrUtils::centroid(void* geom)
{
    return nullptr;
}

void* OgrUtils::getBoundary(void* geom)
{
    return nullptr;
}

void OgrUtils::getEnvelope(void* geom, double* minX, double* maxX, double* minY, double* maxY)
{
    if (minX) *minX = 0.0;
    if (maxX) *maxX = 0.0;
    if (minY) *minY = 0.0;
    if (maxY) *maxY = 0.0;
}

std::string OgrUtils::exportToWkt(void* geom)
{
    return "";
}

void* OgrUtils::importFromWkt(const std::string& wkt)
{
    return nullptr;
}

std::string OgrUtils::exportToWkb(void* geom)
{
    return "";
}

void* OgrUtils::importFromWkb(const std::string& wkb)
{
    return nullptr;
}

std::string OgrUtils::exportToGeoJSON(void* geom)
{
    return "";
}

void* OgrUtils::importFromGeoJSON(const std::string& json)
{
    return nullptr;
}

void* OgrUtils::transform(void* geom, void* ct)
{
    return nullptr;
}

void* OgrUtils::createCoordinateTransformation(void* sourceSRS, void* targetSRS)
{
    return nullptr;
}

void OgrUtils::destroyCoordinateTransformation(void* ct)
{
    // No-op
}

void* OgrUtils::createSpatialReference(const std::string& wkt)
{
    return nullptr;
}

void OgrUtils::destroySpatialReference(void* srs)
{
    // No-op
}

std::string OgrUtils::exportToWkt(void* srs)
{
    return "";
}

std::string OgrUtils::exportToProj4(void* srs)
{
    return "";
}

bool OgrUtils::isSame(void* srs1, void* srs2)
{
    return false;
}

void* OgrUtils::clone(void* srs)
{
    return nullptr;
}

void OgrUtils::morphToESRI(void* srs)
{
    // No-op
}

void OgrUtils::morphFromESRI(void* srs)
{
    // No-op
}

bool OgrUtils::isGeographic(void* srs)
{
    return false;
}

bool OgrUtils::isProjected(void* srs)
{
    return false;
}

bool OgrUtils::isGeocentric(void* srs)
{
    return false;
}

double OgrUtils::getLinearUnits(void* srs)
{
    return 1.0;
}

double OgrUtils::getAngularUnits(void* srs)
{
    return 1.0;
}

std::string OgrUtils::getAuthorityName(void* srs, const std::string& target)
{
    return "";
}

std::string OgrUtils::getAuthorityCode(void* srs, const std::string& target)
{
    return "";
}

void* OgrUtils::getDriver(const std::string& name)
{
    return nullptr;
}

int OgrUtils::getDriverCount()
{
    return 0;
}

void* OgrUtils::getDriver(int i)
{
    return nullptr;
}

void OgrUtils::registerAll()
{
    // No-op
}

void OgrUtils::cleanupAll()
{
    // No-op
}

std::string OgrUtils::getLastError()
{
    return "";
}

void OgrUtils::pushErrorHandler(void* handler)
{
    // No-op
}

void OgrUtils::popErrorHandler()
{
    // No-op
}

void OgrUtils::setConfigOption(const std::string& key, const std::string& value)
{
    // No-op
}

std::string OgrUtils::getConfigOption(const std::string& key, const std::string& defaultValue)
{
    return defaultValue;
}

void OgrUtils::setErrorHandler(void* handler)
{
    // No-op
}

void OgrUtils::setQuiet(bool quiet)
{
    // No-op
}

bool OgrUtils::getQuiet()
{
    return false;
}

void OgrUtils::debug(const std::string& message)
{
    OE_DEBUG << LC << message << std::endl;
}

void OgrUtils::info(const std::string& message)
{
    OE_INFO << LC << message << std::endl;
}

void OgrUtils::notice(const std::string& message)
{
    OE_NOTICE << LC << message << std::endl;
}

void OgrUtils::warn(const std::string& message)
{
    OE_WARN << LC << message << std::endl;
}

void OgrUtils::error(const std::string& message)
{
    OE_WARN << LC << "ERROR: " << message << std::endl;
}
