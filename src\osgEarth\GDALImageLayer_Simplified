/* -*-c++-*- */
/* osgEarth - Geospatial SDK for OpenSceneGraph
 * Copyright 2025 Pelican Mapping
 * http://osgearth.org
 *
 * Simplified GDALImageLayer implementation without GDAL dependencies
 */
#ifndef OSGEARTH_GDAL_IMAGE_LAYER_SIMPLIFIED_H
#define OSGEARTH_GDAL_IMAGE_LAYER_SIMPLIFIED_H 1

#include <osgEarth/Common>
#include <osgEarth/ImageLayer>
#include <osgEarth/URI>

namespace osgEarth
{
    /**
     * Simplified GDALImageLayer for compatibility.
     * This is a stub implementation that provides the interface but no functionality.
     */
    class OSGEARTH_EXPORT GDALImageLayer : public ImageLayer
    {
    public:
        class OSGEARTH_EXPORT Options : public ImageLayer::Options
        {
        public:
            META_LayerOptions(osgEarth, Options, ImageLayer::Options);
            OC_OPTION(URI, url);
            OC_OPTION(std::string, connection);
            OC_OPTION(int, subDataSet);
            OC_OPTION(std::string, subdataset);
            OC_OPTION(bool, interpolateOnRead);
            OC_OPTION(std::string, gdalDriver);
            virtual Config getConfig() const;
        private:
            void fromConfig(const Config& conf);
        };

    public:
        META_Layer(osgEarth, GDALImageLayer, Options, ImageLayer, GDALImage);

        //! URL of the image source
        void setURL(const URI& value);
        const URI& getURL() const;

        //! Connection string
        void setConnection(const std::string& value);
        const std::string& getConnection() const;

        //! Sub-dataset index
        void setSubDataSet(const int& value);
        const int& getSubDataSet() const;

    public: // ImageLayer

        virtual Status openImplementation() override;
        virtual Status closeImplementation() override;
        virtual GeoImage createImageImplementation(const TileKey& key, ProgressCallback* progress) const override;

    protected:
        virtual void init() override;
        virtual ~GDALImageLayer();
    };

    /**
     * Simplified GDALElevationLayer for compatibility.
     */
    class OSGEARTH_EXPORT GDALElevationLayer : public ElevationLayer
    {
    public:
        class OSGEARTH_EXPORT Options : public ElevationLayer::Options
        {
        public:
            META_LayerOptions(osgEarth, Options, ElevationLayer::Options);
            OC_OPTION(URI, url);
            OC_OPTION(std::string, connection);
            OC_OPTION(int, subDataSet);
            OC_OPTION(std::string, subdataset);
            OC_OPTION(bool, interpolateOnRead);
            OC_OPTION(std::string, gdalDriver);
            virtual Config getConfig() const;
        private:
            void fromConfig(const Config& conf);
        };

    public:
        META_Layer(osgEarth, GDALElevationLayer, Options, ElevationLayer, GDALElevation);

        //! URL of the elevation source
        void setURL(const URI& value);
        const URI& getURL() const;

    public: // ElevationLayer

        virtual Status openImplementation() override;
        virtual Status closeImplementation() override;
        virtual GeoHeightField createHeightFieldImplementation(const TileKey& key, ProgressCallback* progress) const override;

    protected:
        virtual void init() override;
        virtual ~GDALElevationLayer();
    };

} // namespace osgEarth

#endif // OSGEARTH_GDAL_IMAGE_LAYER_SIMPLIFIED_H
