// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: glyphs.proto
// Protobuf C++ Version: 5.29.3

#include "glyphs.pb.h"

#include <algorithm>
#include <type_traits>
#include "google/protobuf/io/coded_stream.h"
#include "google/protobuf/generated_message_tctable_impl.h"
#include "google/protobuf/extension_set.h"
#include "google/protobuf/generated_message_util.h"
#include "google/protobuf/wire_format_lite.h"
#include "google/protobuf/io/zero_copy_stream_impl_lite.h"
// @@protoc_insertion_point(includes)

// Must be included last.
#include "google/protobuf/port_def.inc"
PROTOBUF_PRAGMA_INIT_SEG
namespace _pb = ::google::protobuf;
namespace _pbi = ::google::protobuf::internal;
namespace _fl = ::google::protobuf::internal::field_layout;
namespace mapboxgl {
namespace glyphs {

inline constexpr glyph::Impl_::Impl_(
    ::_pbi::ConstantInitialized) noexcept
      : _cached_size_{0},
        bitmap_(
            &::google::protobuf::internal::fixed_address_empty_string,
            ::_pbi::ConstantInitialized()),
        id_{0u},
        width_{0u},
        height_{0u},
        left_{0},
        top_{0},
        advance_{0u} {}

template <typename>
PROTOBUF_CONSTEXPR glyph::glyph(::_pbi::ConstantInitialized)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::MessageLite(_class_data_.base()),
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::MessageLite(),
#endif  // PROTOBUF_CUSTOM_VTABLE
      _impl_(::_pbi::ConstantInitialized()) {
}
struct glyphDefaultTypeInternal {
  PROTOBUF_CONSTEXPR glyphDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~glyphDefaultTypeInternal() {}
  union {
    glyph _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 glyphDefaultTypeInternal _glyph_default_instance_;

inline constexpr fontstack::Impl_::Impl_(
    ::_pbi::ConstantInitialized) noexcept
      : _cached_size_{0},
        glyphs_{},
        name_(
            &::google::protobuf::internal::fixed_address_empty_string,
            ::_pbi::ConstantInitialized()),
        range_(
            &::google::protobuf::internal::fixed_address_empty_string,
            ::_pbi::ConstantInitialized()) {}

template <typename>
PROTOBUF_CONSTEXPR fontstack::fontstack(::_pbi::ConstantInitialized)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::MessageLite(_class_data_.base()),
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::MessageLite(),
#endif  // PROTOBUF_CUSTOM_VTABLE
      _impl_(::_pbi::ConstantInitialized()) {
}
struct fontstackDefaultTypeInternal {
  PROTOBUF_CONSTEXPR fontstackDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~fontstackDefaultTypeInternal() {}
  union {
    fontstack _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 fontstackDefaultTypeInternal _fontstack_default_instance_;

inline constexpr glyphs::Impl_::Impl_(
    ::_pbi::ConstantInitialized) noexcept
      : stacks_{},
        _cached_size_{0} {}

template <typename>
PROTOBUF_CONSTEXPR glyphs::glyphs(::_pbi::ConstantInitialized)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::MessageLite(_class_data_.base()),
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::MessageLite(),
#endif  // PROTOBUF_CUSTOM_VTABLE
      _impl_(::_pbi::ConstantInitialized()) {
}
struct glyphsDefaultTypeInternal {
  PROTOBUF_CONSTEXPR glyphsDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~glyphsDefaultTypeInternal() {}
  union {
    glyphs _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 glyphsDefaultTypeInternal _glyphs_default_instance_;
}  // namespace glyphs
}  // namespace mapboxgl
namespace mapboxgl {
namespace glyphs {
// ===================================================================

class glyph::_Internal {
 public:
  using HasBits =
      decltype(std::declval<glyph>()._impl_._has_bits_);
  static constexpr ::int32_t kHasBitsOffset =
      8 * PROTOBUF_FIELD_OFFSET(glyph, _impl_._has_bits_);
  static bool MissingRequiredFields(const HasBits& has_bits) {
    return ((has_bits[0] & 0x0000007e) ^ 0x0000007e) != 0;
  }
};

glyph::glyph(::google::protobuf::Arena* arena)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::MessageLite(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::MessageLite(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:mapboxgl.glyphs.glyph)
}
inline PROTOBUF_NDEBUG_INLINE glyph::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility, ::google::protobuf::Arena* arena,
    const Impl_& from, const ::mapboxgl::glyphs::glyph& from_msg)
      : _has_bits_{from._has_bits_},
        _cached_size_{0},
        bitmap_(arena, from.bitmap_) {}

glyph::glyph(
    ::google::protobuf::Arena* arena,
    const glyph& from)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::MessageLite(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::MessageLite(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  glyph* const _this = this;
  (void)_this;
  _internal_metadata_.MergeFrom<std::string>(
      from._internal_metadata_);
  new (&_impl_) Impl_(internal_visibility(), arena, from._impl_, from);
  ::memcpy(reinterpret_cast<char *>(&_impl_) +
               offsetof(Impl_, id_),
           reinterpret_cast<const char *>(&from._impl_) +
               offsetof(Impl_, id_),
           offsetof(Impl_, advance_) -
               offsetof(Impl_, id_) +
               sizeof(Impl_::advance_));

  // @@protoc_insertion_point(copy_constructor:mapboxgl.glyphs.glyph)
}
inline PROTOBUF_NDEBUG_INLINE glyph::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility,
    ::google::protobuf::Arena* arena)
      : _cached_size_{0},
        bitmap_(arena) {}

inline void glyph::SharedCtor(::_pb::Arena* arena) {
  new (&_impl_) Impl_(internal_visibility(), arena);
  ::memset(reinterpret_cast<char *>(&_impl_) +
               offsetof(Impl_, id_),
           0,
           offsetof(Impl_, advance_) -
               offsetof(Impl_, id_) +
               sizeof(Impl_::advance_));
}
glyph::~glyph() {
  // @@protoc_insertion_point(destructor:mapboxgl.glyphs.glyph)
  SharedDtor(*this);
}
inline void glyph::SharedDtor(MessageLite& self) {
  glyph& this_ = static_cast<glyph&>(self);
  this_._internal_metadata_.Delete<std::string>();
  ABSL_DCHECK(this_.GetArena() == nullptr);
  this_._impl_.bitmap_.Destroy();
  this_._impl_.~Impl_();
}

inline void* glyph::PlacementNew_(const void*, void* mem,
                                        ::google::protobuf::Arena* arena) {
  return ::new (mem) glyph(arena);
}
constexpr auto glyph::InternalNewImpl_() {
  return ::google::protobuf::internal::MessageCreator::CopyInit(sizeof(glyph),
                                            alignof(glyph));
}
PROTOBUF_CONSTINIT
PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::google::protobuf::internal::ClassDataLite<22> glyph::_class_data_ = {
    {
        &_glyph_default_instance_._instance,
        &_table_.header,
        nullptr,  // OnDemandRegisterArenaDtor
        glyph::IsInitializedImpl,
        &glyph::MergeImpl,
        ::google::protobuf::MessageLite::GetNewImpl<glyph>(),
#if defined(PROTOBUF_CUSTOM_VTABLE)
        &glyph::SharedDtor,
        ::google::protobuf::MessageLite::GetClearImpl<glyph>(), &glyph::ByteSizeLong,
            &glyph::_InternalSerialize,
#endif  // PROTOBUF_CUSTOM_VTABLE
        PROTOBUF_FIELD_OFFSET(glyph, _impl_._cached_size_),
        true,
    },
    "mapboxgl.glyphs.glyph",
};
const ::google::protobuf::internal::ClassData* glyph::GetClassData() const {
  return _class_data_.base();
}
PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<3, 7, 0, 0, 2> glyph::_table_ = {
  {
    PROTOBUF_FIELD_OFFSET(glyph, _impl_._has_bits_),
    0, // no _extensions_
    7, 56,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294967168,  // skipmap
    offsetof(decltype(_table_), field_entries),
    7,  // num_field_entries
    0,  // num_aux_entries
    offsetof(decltype(_table_), field_names),  // no aux_entries
    _class_data_.base(),
    nullptr,  // post_loop_handler
    ::_pbi::TcParser::GenericFallbackLite,  // fallback
    #ifdef PROTOBUF_PREFETCH_PARSE_TABLE
    ::_pbi::TcParser::GetTable<::mapboxgl::glyphs::glyph>(),  // to_prefetch
    #endif  // PROTOBUF_PREFETCH_PARSE_TABLE
  }, {{
    {::_pbi::TcParser::MiniParse, {}},
    // required uint32 id = 1;
    {::_pbi::TcParser::FastV32S1,
     {8, 1, 0, PROTOBUF_FIELD_OFFSET(glyph, _impl_.id_)}},
    // optional bytes bitmap = 2;
    {::_pbi::TcParser::FastBS1,
     {18, 0, 0, PROTOBUF_FIELD_OFFSET(glyph, _impl_.bitmap_)}},
    // required uint32 width = 3;
    {::_pbi::TcParser::FastV32S1,
     {24, 2, 0, PROTOBUF_FIELD_OFFSET(glyph, _impl_.width_)}},
    // required uint32 height = 4;
    {::_pbi::TcParser::FastV32S1,
     {32, 3, 0, PROTOBUF_FIELD_OFFSET(glyph, _impl_.height_)}},
    // required sint32 left = 5;
    {::_pbi::TcParser::FastZ32S1,
     {40, 4, 0, PROTOBUF_FIELD_OFFSET(glyph, _impl_.left_)}},
    // required sint32 top = 6;
    {::_pbi::TcParser::FastZ32S1,
     {48, 5, 0, PROTOBUF_FIELD_OFFSET(glyph, _impl_.top_)}},
    // required uint32 advance = 7;
    {::_pbi::TcParser::FastV32S1,
     {56, 6, 0, PROTOBUF_FIELD_OFFSET(glyph, _impl_.advance_)}},
  }}, {{
    65535, 65535
  }}, {{
    // required uint32 id = 1;
    {PROTOBUF_FIELD_OFFSET(glyph, _impl_.id_), _Internal::kHasBitsOffset + 1, 0,
    (0 | ::_fl::kFcOptional | ::_fl::kUInt32)},
    // optional bytes bitmap = 2;
    {PROTOBUF_FIELD_OFFSET(glyph, _impl_.bitmap_), _Internal::kHasBitsOffset + 0, 0,
    (0 | ::_fl::kFcOptional | ::_fl::kBytes | ::_fl::kRepAString)},
    // required uint32 width = 3;
    {PROTOBUF_FIELD_OFFSET(glyph, _impl_.width_), _Internal::kHasBitsOffset + 2, 0,
    (0 | ::_fl::kFcOptional | ::_fl::kUInt32)},
    // required uint32 height = 4;
    {PROTOBUF_FIELD_OFFSET(glyph, _impl_.height_), _Internal::kHasBitsOffset + 3, 0,
    (0 | ::_fl::kFcOptional | ::_fl::kUInt32)},
    // required sint32 left = 5;
    {PROTOBUF_FIELD_OFFSET(glyph, _impl_.left_), _Internal::kHasBitsOffset + 4, 0,
    (0 | ::_fl::kFcOptional | ::_fl::kSInt32)},
    // required sint32 top = 6;
    {PROTOBUF_FIELD_OFFSET(glyph, _impl_.top_), _Internal::kHasBitsOffset + 5, 0,
    (0 | ::_fl::kFcOptional | ::_fl::kSInt32)},
    // required uint32 advance = 7;
    {PROTOBUF_FIELD_OFFSET(glyph, _impl_.advance_), _Internal::kHasBitsOffset + 6, 0,
    (0 | ::_fl::kFcOptional | ::_fl::kUInt32)},
  }},
  // no aux_entries
  {{
  }},
};

PROTOBUF_NOINLINE void glyph::Clear() {
// @@protoc_insertion_point(message_clear_start:mapboxgl.glyphs.glyph)
  ::google::protobuf::internal::TSanWrite(&_impl_);
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _impl_._has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    _impl_.bitmap_.ClearNonDefaultToEmpty();
  }
  if (cached_has_bits & 0x0000007eu) {
    ::memset(&_impl_.id_, 0, static_cast<::size_t>(
        reinterpret_cast<char*>(&_impl_.advance_) -
        reinterpret_cast<char*>(&_impl_.id_)) + sizeof(_impl_.advance_));
  }
  _impl_._has_bits_.Clear();
  _internal_metadata_.Clear<std::string>();
}

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::uint8_t* glyph::_InternalSerialize(
            const MessageLite& base, ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) {
          const glyph& this_ = static_cast<const glyph&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::uint8_t* glyph::_InternalSerialize(
            ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) const {
          const glyph& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(serialize_to_array_start:mapboxgl.glyphs.glyph)
          ::uint32_t cached_has_bits = 0;
          (void)cached_has_bits;

          cached_has_bits = this_._impl_._has_bits_[0];
          // required uint32 id = 1;
          if (cached_has_bits & 0x00000002u) {
            target = stream->EnsureSpace(target);
            target = ::_pbi::WireFormatLite::WriteUInt32ToArray(
                1, this_._internal_id(), target);
          }

          // optional bytes bitmap = 2;
          if (cached_has_bits & 0x00000001u) {
            const std::string& _s = this_._internal_bitmap();
            target = stream->WriteBytesMaybeAliased(2, _s, target);
          }

          // required uint32 width = 3;
          if (cached_has_bits & 0x00000004u) {
            target = stream->EnsureSpace(target);
            target = ::_pbi::WireFormatLite::WriteUInt32ToArray(
                3, this_._internal_width(), target);
          }

          // required uint32 height = 4;
          if (cached_has_bits & 0x00000008u) {
            target = stream->EnsureSpace(target);
            target = ::_pbi::WireFormatLite::WriteUInt32ToArray(
                4, this_._internal_height(), target);
          }

          // required sint32 left = 5;
          if (cached_has_bits & 0x00000010u) {
            target = stream->EnsureSpace(target);
            target = ::_pbi::WireFormatLite::WriteSInt32ToArray(
                5, this_._internal_left(), target);
          }

          // required sint32 top = 6;
          if (cached_has_bits & 0x00000020u) {
            target = stream->EnsureSpace(target);
            target = ::_pbi::WireFormatLite::WriteSInt32ToArray(
                6, this_._internal_top(), target);
          }

          // required uint32 advance = 7;
          if (cached_has_bits & 0x00000040u) {
            target = stream->EnsureSpace(target);
            target = ::_pbi::WireFormatLite::WriteUInt32ToArray(
                7, this_._internal_advance(), target);
          }

          if (PROTOBUF_PREDICT_FALSE(this_._internal_metadata_.have_unknown_fields())) {
            target = stream->WriteRaw(
                this_._internal_metadata_.unknown_fields<std::string>(::google::protobuf::internal::GetEmptyString).data(),
                static_cast<int>(this_._internal_metadata_.unknown_fields<std::string>(::google::protobuf::internal::GetEmptyString).size()), target);
          }
          // @@protoc_insertion_point(serialize_to_array_end:mapboxgl.glyphs.glyph)
          return target;
        }

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::size_t glyph::ByteSizeLong(const MessageLite& base) {
          const glyph& this_ = static_cast<const glyph&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::size_t glyph::ByteSizeLong() const {
          const glyph& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(message_byte_size_start:mapboxgl.glyphs.glyph)
          ::size_t total_size = 0;

          ::uint32_t cached_has_bits = 0;
          // Prevent compiler warnings about cached_has_bits being unused
          (void)cached_has_bits;

          ::_pbi::Prefetch5LinesFrom7Lines(&this_);
           {
            // optional bytes bitmap = 2;
            cached_has_bits = this_._impl_._has_bits_[0];
            if (cached_has_bits & 0x00000001u) {
              total_size += 1 + ::google::protobuf::internal::WireFormatLite::BytesSize(
                                              this_._internal_bitmap());
            }
          }
          if (cached_has_bits & 0x0000007eu) {
            // required uint32 id = 1;
            if (cached_has_bits & 0x00000002u) {
              total_size += ::_pbi::WireFormatLite::UInt32SizePlusOne(
                  this_._internal_id());
            }
            // required uint32 width = 3;
            if (cached_has_bits & 0x00000004u) {
              total_size += ::_pbi::WireFormatLite::UInt32SizePlusOne(
                  this_._internal_width());
            }
            // required uint32 height = 4;
            if (cached_has_bits & 0x00000008u) {
              total_size += ::_pbi::WireFormatLite::UInt32SizePlusOne(
                  this_._internal_height());
            }
            // required sint32 left = 5;
            if (cached_has_bits & 0x00000010u) {
              total_size += ::_pbi::WireFormatLite::SInt32SizePlusOne(
                  this_._internal_left());
            }
            // required sint32 top = 6;
            if (cached_has_bits & 0x00000020u) {
              total_size += ::_pbi::WireFormatLite::SInt32SizePlusOne(
                  this_._internal_top());
            }
            // required uint32 advance = 7;
            if (cached_has_bits & 0x00000040u) {
              total_size += ::_pbi::WireFormatLite::UInt32SizePlusOne(
                  this_._internal_advance());
            }
          }
          if (PROTOBUF_PREDICT_FALSE(this_._internal_metadata_.have_unknown_fields())) {
            total_size += this_._internal_metadata_.unknown_fields<std::string>(::google::protobuf::internal::GetEmptyString).size();
          }
          this_._impl_._cached_size_.Set(::_pbi::ToCachedSize(total_size));
          return total_size;
        }

void glyph::MergeImpl(::google::protobuf::MessageLite& to_msg, const ::google::protobuf::MessageLite& from_msg) {
  auto* const _this = static_cast<glyph*>(&to_msg);
  auto& from = static_cast<const glyph&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:mapboxgl.glyphs.glyph)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._impl_._has_bits_[0];
  if (cached_has_bits & 0x0000007fu) {
    if (cached_has_bits & 0x00000001u) {
      _this->_internal_set_bitmap(from._internal_bitmap());
    }
    if (cached_has_bits & 0x00000002u) {
      _this->_impl_.id_ = from._impl_.id_;
    }
    if (cached_has_bits & 0x00000004u) {
      _this->_impl_.width_ = from._impl_.width_;
    }
    if (cached_has_bits & 0x00000008u) {
      _this->_impl_.height_ = from._impl_.height_;
    }
    if (cached_has_bits & 0x00000010u) {
      _this->_impl_.left_ = from._impl_.left_;
    }
    if (cached_has_bits & 0x00000020u) {
      _this->_impl_.top_ = from._impl_.top_;
    }
    if (cached_has_bits & 0x00000040u) {
      _this->_impl_.advance_ = from._impl_.advance_;
    }
  }
  _this->_impl_._has_bits_[0] |= cached_has_bits;
  _this->_internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
}

void glyph::CopyFrom(const glyph& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:mapboxgl.glyphs.glyph)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

PROTOBUF_NOINLINE bool glyph::IsInitializedImpl(
    const MessageLite& msg) {
  auto& this_ = static_cast<const glyph&>(msg);
  if (_Internal::MissingRequiredFields(this_._impl_._has_bits_)) {
    return false;
  }
  return true;
}

void glyph::InternalSwap(glyph* PROTOBUF_RESTRICT other) {
  using std::swap;
  auto* arena = GetArena();
  ABSL_DCHECK_EQ(arena, other->GetArena());
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_impl_._has_bits_[0], other->_impl_._has_bits_[0]);
  ::_pbi::ArenaStringPtr::InternalSwap(&_impl_.bitmap_, &other->_impl_.bitmap_, arena);
  ::google::protobuf::internal::memswap<
      PROTOBUF_FIELD_OFFSET(glyph, _impl_.advance_)
      + sizeof(glyph::_impl_.advance_)
      - PROTOBUF_FIELD_OFFSET(glyph, _impl_.id_)>(
          reinterpret_cast<char*>(&_impl_.id_),
          reinterpret_cast<char*>(&other->_impl_.id_));
}

// ===================================================================

class fontstack::_Internal {
 public:
  using HasBits =
      decltype(std::declval<fontstack>()._impl_._has_bits_);
  static constexpr ::int32_t kHasBitsOffset =
      8 * PROTOBUF_FIELD_OFFSET(fontstack, _impl_._has_bits_);
  static bool MissingRequiredFields(const HasBits& has_bits) {
    return ((has_bits[0] & 0x00000003) ^ 0x00000003) != 0;
  }
};

fontstack::fontstack(::google::protobuf::Arena* arena)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::MessageLite(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::MessageLite(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:mapboxgl.glyphs.fontstack)
}
inline PROTOBUF_NDEBUG_INLINE fontstack::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility, ::google::protobuf::Arena* arena,
    const Impl_& from, const ::mapboxgl::glyphs::fontstack& from_msg)
      : _has_bits_{from._has_bits_},
        _cached_size_{0},
        glyphs_{visibility, arena, from.glyphs_},
        name_(arena, from.name_),
        range_(arena, from.range_) {}

fontstack::fontstack(
    ::google::protobuf::Arena* arena,
    const fontstack& from)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::MessageLite(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::MessageLite(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  fontstack* const _this = this;
  (void)_this;
  _internal_metadata_.MergeFrom<std::string>(
      from._internal_metadata_);
  new (&_impl_) Impl_(internal_visibility(), arena, from._impl_, from);

  // @@protoc_insertion_point(copy_constructor:mapboxgl.glyphs.fontstack)
}
inline PROTOBUF_NDEBUG_INLINE fontstack::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility,
    ::google::protobuf::Arena* arena)
      : _cached_size_{0},
        glyphs_{visibility, arena},
        name_(arena),
        range_(arena) {}

inline void fontstack::SharedCtor(::_pb::Arena* arena) {
  new (&_impl_) Impl_(internal_visibility(), arena);
}
fontstack::~fontstack() {
  // @@protoc_insertion_point(destructor:mapboxgl.glyphs.fontstack)
  SharedDtor(*this);
}
inline void fontstack::SharedDtor(MessageLite& self) {
  fontstack& this_ = static_cast<fontstack&>(self);
  this_._internal_metadata_.Delete<std::string>();
  ABSL_DCHECK(this_.GetArena() == nullptr);
  this_._impl_.name_.Destroy();
  this_._impl_.range_.Destroy();
  this_._impl_.~Impl_();
}

inline void* fontstack::PlacementNew_(const void*, void* mem,
                                        ::google::protobuf::Arena* arena) {
  return ::new (mem) fontstack(arena);
}
constexpr auto fontstack::InternalNewImpl_() {
  constexpr auto arena_bits = ::google::protobuf::internal::EncodePlacementArenaOffsets({
      PROTOBUF_FIELD_OFFSET(fontstack, _impl_.glyphs_) +
          decltype(fontstack::_impl_.glyphs_)::
              InternalGetArenaOffset(
                  ::google::protobuf::MessageLite::internal_visibility()),
  });
  if (arena_bits.has_value()) {
    return ::google::protobuf::internal::MessageCreator::CopyInit(
        sizeof(fontstack), alignof(fontstack), *arena_bits);
  } else {
    return ::google::protobuf::internal::MessageCreator(&fontstack::PlacementNew_,
                                 sizeof(fontstack),
                                 alignof(fontstack));
  }
}
PROTOBUF_CONSTINIT
PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::google::protobuf::internal::ClassDataLite<26> fontstack::_class_data_ = {
    {
        &_fontstack_default_instance_._instance,
        &_table_.header,
        nullptr,  // OnDemandRegisterArenaDtor
        fontstack::IsInitializedImpl,
        &fontstack::MergeImpl,
        ::google::protobuf::MessageLite::GetNewImpl<fontstack>(),
#if defined(PROTOBUF_CUSTOM_VTABLE)
        &fontstack::SharedDtor,
        ::google::protobuf::MessageLite::GetClearImpl<fontstack>(), &fontstack::ByteSizeLong,
            &fontstack::_InternalSerialize,
#endif  // PROTOBUF_CUSTOM_VTABLE
        PROTOBUF_FIELD_OFFSET(fontstack, _impl_._cached_size_),
        true,
    },
    "mapboxgl.glyphs.fontstack",
};
const ::google::protobuf::internal::ClassData* fontstack::GetClassData() const {
  return _class_data_.base();
}
PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<2, 3, 1, 0, 2> fontstack::_table_ = {
  {
    PROTOBUF_FIELD_OFFSET(fontstack, _impl_._has_bits_),
    0, // no _extensions_
    3, 24,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294967288,  // skipmap
    offsetof(decltype(_table_), field_entries),
    3,  // num_field_entries
    1,  // num_aux_entries
    offsetof(decltype(_table_), aux_entries),
    _class_data_.base(),
    nullptr,  // post_loop_handler
    ::_pbi::TcParser::GenericFallbackLite,  // fallback
    #ifdef PROTOBUF_PREFETCH_PARSE_TABLE
    ::_pbi::TcParser::GetTable<::mapboxgl::glyphs::fontstack>(),  // to_prefetch
    #endif  // PROTOBUF_PREFETCH_PARSE_TABLE
  }, {{
    {::_pbi::TcParser::MiniParse, {}},
    // required string name = 1;
    {::_pbi::TcParser::FastBS1,
     {10, 0, 0, PROTOBUF_FIELD_OFFSET(fontstack, _impl_.name_)}},
    // required string range = 2;
    {::_pbi::TcParser::FastBS1,
     {18, 1, 0, PROTOBUF_FIELD_OFFSET(fontstack, _impl_.range_)}},
    // repeated .mapboxgl.glyphs.glyph glyphs = 3;
    {::_pbi::TcParser::FastMtR1,
     {26, 63, 0, PROTOBUF_FIELD_OFFSET(fontstack, _impl_.glyphs_)}},
  }}, {{
    65535, 65535
  }}, {{
    // required string name = 1;
    {PROTOBUF_FIELD_OFFSET(fontstack, _impl_.name_), _Internal::kHasBitsOffset + 0, 0,
    (0 | ::_fl::kFcOptional | ::_fl::kBytes | ::_fl::kRepAString)},
    // required string range = 2;
    {PROTOBUF_FIELD_OFFSET(fontstack, _impl_.range_), _Internal::kHasBitsOffset + 1, 0,
    (0 | ::_fl::kFcOptional | ::_fl::kBytes | ::_fl::kRepAString)},
    // repeated .mapboxgl.glyphs.glyph glyphs = 3;
    {PROTOBUF_FIELD_OFFSET(fontstack, _impl_.glyphs_), -1, 0,
    (0 | ::_fl::kFcRepeated | ::_fl::kMessage | ::_fl::kTvTable)},
  }}, {{
    {::_pbi::TcParser::GetTable<::mapboxgl::glyphs::glyph>()},
  }}, {{
  }},
};

PROTOBUF_NOINLINE void fontstack::Clear() {
// @@protoc_insertion_point(message_clear_start:mapboxgl.glyphs.fontstack)
  ::google::protobuf::internal::TSanWrite(&_impl_);
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.glyphs_.Clear();
  cached_has_bits = _impl_._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      _impl_.name_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000002u) {
      _impl_.range_.ClearNonDefaultToEmpty();
    }
  }
  _impl_._has_bits_.Clear();
  _internal_metadata_.Clear<std::string>();
}

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::uint8_t* fontstack::_InternalSerialize(
            const MessageLite& base, ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) {
          const fontstack& this_ = static_cast<const fontstack&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::uint8_t* fontstack::_InternalSerialize(
            ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) const {
          const fontstack& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(serialize_to_array_start:mapboxgl.glyphs.fontstack)
          ::uint32_t cached_has_bits = 0;
          (void)cached_has_bits;

          cached_has_bits = this_._impl_._has_bits_[0];
          // required string name = 1;
          if (cached_has_bits & 0x00000001u) {
            const std::string& _s = this_._internal_name();
            target = stream->WriteStringMaybeAliased(1, _s, target);
          }

          // required string range = 2;
          if (cached_has_bits & 0x00000002u) {
            const std::string& _s = this_._internal_range();
            target = stream->WriteStringMaybeAliased(2, _s, target);
          }

          // repeated .mapboxgl.glyphs.glyph glyphs = 3;
          for (unsigned i = 0, n = static_cast<unsigned>(
                                   this_._internal_glyphs_size());
               i < n; i++) {
            const auto& repfield = this_._internal_glyphs().Get(i);
            target =
                ::google::protobuf::internal::WireFormatLite::InternalWriteMessage(
                    3, repfield, repfield.GetCachedSize(),
                    target, stream);
          }

          if (PROTOBUF_PREDICT_FALSE(this_._internal_metadata_.have_unknown_fields())) {
            target = stream->WriteRaw(
                this_._internal_metadata_.unknown_fields<std::string>(::google::protobuf::internal::GetEmptyString).data(),
                static_cast<int>(this_._internal_metadata_.unknown_fields<std::string>(::google::protobuf::internal::GetEmptyString).size()), target);
          }
          // @@protoc_insertion_point(serialize_to_array_end:mapboxgl.glyphs.fontstack)
          return target;
        }

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::size_t fontstack::ByteSizeLong(const MessageLite& base) {
          const fontstack& this_ = static_cast<const fontstack&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::size_t fontstack::ByteSizeLong() const {
          const fontstack& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(message_byte_size_start:mapboxgl.glyphs.fontstack)
          ::size_t total_size = 0;

          ::uint32_t cached_has_bits = 0;
          // Prevent compiler warnings about cached_has_bits being unused
          (void)cached_has_bits;

          ::_pbi::Prefetch5LinesFrom7Lines(&this_);
           {
            // repeated .mapboxgl.glyphs.glyph glyphs = 3;
            {
              total_size += 1UL * this_._internal_glyphs_size();
              for (const auto& msg : this_._internal_glyphs()) {
                total_size += ::google::protobuf::internal::WireFormatLite::MessageSize(msg);
              }
            }
          }
          cached_has_bits = this_._impl_._has_bits_[0];
          if (cached_has_bits & 0x00000003u) {
            // required string name = 1;
            if (cached_has_bits & 0x00000001u) {
              total_size += 1 + ::google::protobuf::internal::WireFormatLite::StringSize(
                                              this_._internal_name());
            }
            // required string range = 2;
            if (cached_has_bits & 0x00000002u) {
              total_size += 1 + ::google::protobuf::internal::WireFormatLite::StringSize(
                                              this_._internal_range());
            }
          }
          if (PROTOBUF_PREDICT_FALSE(this_._internal_metadata_.have_unknown_fields())) {
            total_size += this_._internal_metadata_.unknown_fields<std::string>(::google::protobuf::internal::GetEmptyString).size();
          }
          this_._impl_._cached_size_.Set(::_pbi::ToCachedSize(total_size));
          return total_size;
        }

void fontstack::MergeImpl(::google::protobuf::MessageLite& to_msg, const ::google::protobuf::MessageLite& from_msg) {
  auto* const _this = static_cast<fontstack*>(&to_msg);
  auto& from = static_cast<const fontstack&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:mapboxgl.glyphs.fontstack)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_internal_mutable_glyphs()->MergeFrom(
      from._internal_glyphs());
  cached_has_bits = from._impl_._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      _this->_internal_set_name(from._internal_name());
    }
    if (cached_has_bits & 0x00000002u) {
      _this->_internal_set_range(from._internal_range());
    }
  }
  _this->_impl_._has_bits_[0] |= cached_has_bits;
  _this->_internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
}

void fontstack::CopyFrom(const fontstack& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:mapboxgl.glyphs.fontstack)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

PROTOBUF_NOINLINE bool fontstack::IsInitializedImpl(
    const MessageLite& msg) {
  auto& this_ = static_cast<const fontstack&>(msg);
  if (_Internal::MissingRequiredFields(this_._impl_._has_bits_)) {
    return false;
  }
  if (!::google::protobuf::internal::AllAreInitialized(this_._internal_glyphs()))
    return false;
  return true;
}

void fontstack::InternalSwap(fontstack* PROTOBUF_RESTRICT other) {
  using std::swap;
  auto* arena = GetArena();
  ABSL_DCHECK_EQ(arena, other->GetArena());
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_impl_._has_bits_[0], other->_impl_._has_bits_[0]);
  _impl_.glyphs_.InternalSwap(&other->_impl_.glyphs_);
  ::_pbi::ArenaStringPtr::InternalSwap(&_impl_.name_, &other->_impl_.name_, arena);
  ::_pbi::ArenaStringPtr::InternalSwap(&_impl_.range_, &other->_impl_.range_, arena);
}

// ===================================================================

class glyphs::_Internal {
 public:
};

glyphs::glyphs(::google::protobuf::Arena* arena)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::MessageLite(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::MessageLite(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:mapboxgl.glyphs.glyphs)
}
inline PROTOBUF_NDEBUG_INLINE glyphs::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility, ::google::protobuf::Arena* arena,
    const Impl_& from, const ::mapboxgl::glyphs::glyphs& from_msg)
      : _extensions_{visibility, arena},
        stacks_{visibility, arena, from.stacks_},
        _cached_size_{0} {}

glyphs::glyphs(
    ::google::protobuf::Arena* arena,
    const glyphs& from)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::MessageLite(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::MessageLite(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  glyphs* const _this = this;
  (void)_this;
  _internal_metadata_.MergeFrom<std::string>(
      from._internal_metadata_);
  new (&_impl_) Impl_(internal_visibility(), arena, from._impl_, from);
  _impl_._extensions_.MergeFrom(this, from._impl_._extensions_);

  // @@protoc_insertion_point(copy_constructor:mapboxgl.glyphs.glyphs)
}
inline PROTOBUF_NDEBUG_INLINE glyphs::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility,
    ::google::protobuf::Arena* arena)
      : _extensions_{visibility, arena},
        stacks_{visibility, arena},
        _cached_size_{0} {}

inline void glyphs::SharedCtor(::_pb::Arena* arena) {
  new (&_impl_) Impl_(internal_visibility(), arena);
}
glyphs::~glyphs() {
  // @@protoc_insertion_point(destructor:mapboxgl.glyphs.glyphs)
  SharedDtor(*this);
}
inline void glyphs::SharedDtor(MessageLite& self) {
  glyphs& this_ = static_cast<glyphs&>(self);
  this_._internal_metadata_.Delete<std::string>();
  ABSL_DCHECK(this_.GetArena() == nullptr);
  this_._impl_.~Impl_();
}

inline void* glyphs::PlacementNew_(const void*, void* mem,
                                        ::google::protobuf::Arena* arena) {
  return ::new (mem) glyphs(arena);
}
constexpr auto glyphs::InternalNewImpl_() {
  constexpr auto arena_bits = ::google::protobuf::internal::EncodePlacementArenaOffsets({
      PROTOBUF_FIELD_OFFSET(glyphs, _impl_._extensions_) +
          decltype(glyphs::_impl_._extensions_)::InternalGetArenaOffset(
              ::google::protobuf::MessageLite::internal_visibility()),
      PROTOBUF_FIELD_OFFSET(glyphs, _impl_.stacks_) +
          decltype(glyphs::_impl_.stacks_)::
              InternalGetArenaOffset(
                  ::google::protobuf::MessageLite::internal_visibility()),
  });
  if (arena_bits.has_value()) {
    return ::google::protobuf::internal::MessageCreator::ZeroInit(
        sizeof(glyphs), alignof(glyphs), *arena_bits);
  } else {
    return ::google::protobuf::internal::MessageCreator(&glyphs::PlacementNew_,
                                 sizeof(glyphs),
                                 alignof(glyphs));
  }
}
PROTOBUF_CONSTINIT
PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::google::protobuf::internal::ClassDataLite<23> glyphs::_class_data_ = {
    {
        &_glyphs_default_instance_._instance,
        &_table_.header,
        nullptr,  // OnDemandRegisterArenaDtor
        glyphs::IsInitializedImpl,
        &glyphs::MergeImpl,
        ::google::protobuf::MessageLite::GetNewImpl<glyphs>(),
#if defined(PROTOBUF_CUSTOM_VTABLE)
        &glyphs::SharedDtor,
        ::google::protobuf::MessageLite::GetClearImpl<glyphs>(), &glyphs::ByteSizeLong,
            &glyphs::_InternalSerialize,
#endif  // PROTOBUF_CUSTOM_VTABLE
        PROTOBUF_FIELD_OFFSET(glyphs, _impl_._cached_size_),
        true,
    },
    "mapboxgl.glyphs.glyphs",
};
const ::google::protobuf::internal::ClassData* glyphs::GetClassData() const {
  return _class_data_.base();
}
PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<0, 1, 1, 0, 2> glyphs::_table_ = {
  {
    0,  // no _has_bits_
    PROTOBUF_FIELD_OFFSET(glyphs, _impl_._extensions_),
    1, 0,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294967294,  // skipmap
    offsetof(decltype(_table_), field_entries),
    1,  // num_field_entries
    1,  // num_aux_entries
    offsetof(decltype(_table_), aux_entries),
    _class_data_.base(),
    nullptr,  // post_loop_handler
    ::_pbi::TcParser::GenericFallbackLite,  // fallback
    #ifdef PROTOBUF_PREFETCH_PARSE_TABLE
    ::_pbi::TcParser::GetTable<::mapboxgl::glyphs::glyphs>(),  // to_prefetch
    #endif  // PROTOBUF_PREFETCH_PARSE_TABLE
  }, {{
    // repeated .mapboxgl.glyphs.fontstack stacks = 1;
    {::_pbi::TcParser::FastMtR1,
     {10, 63, 0, PROTOBUF_FIELD_OFFSET(glyphs, _impl_.stacks_)}},
  }}, {{
    65535, 65535
  }}, {{
    // repeated .mapboxgl.glyphs.fontstack stacks = 1;
    {PROTOBUF_FIELD_OFFSET(glyphs, _impl_.stacks_), 0, 0,
    (0 | ::_fl::kFcRepeated | ::_fl::kMessage | ::_fl::kTvTable)},
  }}, {{
    {::_pbi::TcParser::GetTable<::mapboxgl::glyphs::fontstack>()},
  }}, {{
  }},
};

PROTOBUF_NOINLINE void glyphs::Clear() {
// @@protoc_insertion_point(message_clear_start:mapboxgl.glyphs.glyphs)
  ::google::protobuf::internal::TSanWrite(&_impl_);
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_._extensions_.Clear();
  _impl_.stacks_.Clear();
  _internal_metadata_.Clear<std::string>();
}

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::uint8_t* glyphs::_InternalSerialize(
            const MessageLite& base, ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) {
          const glyphs& this_ = static_cast<const glyphs&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::uint8_t* glyphs::_InternalSerialize(
            ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) const {
          const glyphs& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(serialize_to_array_start:mapboxgl.glyphs.glyphs)
          ::uint32_t cached_has_bits = 0;
          (void)cached_has_bits;

          // repeated .mapboxgl.glyphs.fontstack stacks = 1;
          for (unsigned i = 0, n = static_cast<unsigned>(
                                   this_._internal_stacks_size());
               i < n; i++) {
            const auto& repfield = this_._internal_stacks().Get(i);
            target =
                ::google::protobuf::internal::WireFormatLite::InternalWriteMessage(
                    1, repfield, repfield.GetCachedSize(),
                    target, stream);
          }

          // Extension range [16, 8192)
          target = this_._impl_._extensions_._InternalSerialize(
              internal_default_instance(), 16, 8192, target, stream);
          if (PROTOBUF_PREDICT_FALSE(this_._internal_metadata_.have_unknown_fields())) {
            target = stream->WriteRaw(
                this_._internal_metadata_.unknown_fields<std::string>(::google::protobuf::internal::GetEmptyString).data(),
                static_cast<int>(this_._internal_metadata_.unknown_fields<std::string>(::google::protobuf::internal::GetEmptyString).size()), target);
          }
          // @@protoc_insertion_point(serialize_to_array_end:mapboxgl.glyphs.glyphs)
          return target;
        }

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::size_t glyphs::ByteSizeLong(const MessageLite& base) {
          const glyphs& this_ = static_cast<const glyphs&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::size_t glyphs::ByteSizeLong() const {
          const glyphs& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(message_byte_size_start:mapboxgl.glyphs.glyphs)
          ::size_t total_size = 0;
          total_size += this_._impl_._extensions_.ByteSize();

          ::uint32_t cached_has_bits = 0;
          // Prevent compiler warnings about cached_has_bits being unused
          (void)cached_has_bits;

          ::_pbi::Prefetch5LinesFrom7Lines(&this_);
           {
            // repeated .mapboxgl.glyphs.fontstack stacks = 1;
            {
              total_size += 1UL * this_._internal_stacks_size();
              for (const auto& msg : this_._internal_stacks()) {
                total_size += ::google::protobuf::internal::WireFormatLite::MessageSize(msg);
              }
            }
          }
          if (PROTOBUF_PREDICT_FALSE(this_._internal_metadata_.have_unknown_fields())) {
            total_size += this_._internal_metadata_.unknown_fields<std::string>(::google::protobuf::internal::GetEmptyString).size();
          }
          this_._impl_._cached_size_.Set(::_pbi::ToCachedSize(total_size));
          return total_size;
        }

void glyphs::MergeImpl(::google::protobuf::MessageLite& to_msg, const ::google::protobuf::MessageLite& from_msg) {
  auto* const _this = static_cast<glyphs*>(&to_msg);
  auto& from = static_cast<const glyphs&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:mapboxgl.glyphs.glyphs)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_internal_mutable_stacks()->MergeFrom(
      from._internal_stacks());
  _this->_impl_._extensions_.MergeFrom(internal_default_instance(), from._impl_._extensions_);
  _this->_internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
}

void glyphs::CopyFrom(const glyphs& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:mapboxgl.glyphs.glyphs)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

PROTOBUF_NOINLINE bool glyphs::IsInitializedImpl(
    const MessageLite& msg) {
  auto& this_ = static_cast<const glyphs&>(msg);
  if (!this_._impl_._extensions_.IsInitialized(
          internal_default_instance())) {
    return false;
  }
  if (!::google::protobuf::internal::AllAreInitialized(this_._internal_stacks()))
    return false;
  return true;
}

void glyphs::InternalSwap(glyphs* PROTOBUF_RESTRICT other) {
  using std::swap;
  _impl_._extensions_.InternalSwap(&other->_impl_._extensions_);
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.stacks_.InternalSwap(&other->_impl_.stacks_);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace glyphs
}  // namespace mapboxgl
namespace google {
namespace protobuf {
}  // namespace protobuf
}  // namespace google
// @@protoc_insertion_point(global_scope)
#include "google/protobuf/port_undef.inc"
