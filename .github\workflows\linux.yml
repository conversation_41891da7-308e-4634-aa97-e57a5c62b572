name: linux-x64

on: [push, pull_request, workflow_dispatch]

env:
  # Customize the CMake build type here (Release, Debug, RelWithDebInfo, etc.)
  BUILD_TYPE: Release

jobs:

  build:
    runs-on: ${{ matrix.os }}

    strategy:
      matrix:
        os: ['ubuntu-24.04']
        include:
          - os: 'ubuntu-24.04'
            triplet: 'x64-linux'

    steps:
    - name: Setup environment
      run: |
        sudo apt-get update -qq
        sudo apt-get install libgl-dev
        sudo apt-get install -y cmake
        sudo apt-get install -y libopenscenegraph-dev
        sudo apt-get install -y libgdal-dev
        sudo apt-get install -y libgeos-dev
        sudo apt-get install -y libsqlite3-dev
        sudo apt-get install -y protobuf-compiler libprotobuf-dev
        sudo apt-get install -y libtinyxml-dev
        sudo apt-get install -y libglew-dev

    - uses: actions/checkout@v4
      with:
        submodules: recursive

    - name: Create Build Environment
      run: |
        cmake -E make_directory ${{runner.workspace}}/build

    - name: Configure CMake
      shell: bash
      working-directory: ${{ runner.workspace }}/build
      run: cmake $GITHUB_WORKSPACE -DCMAKE_BUILD_TYPE=$BUILD_TYPE -DOSGEARTH_BUILD_LEGACY_SPLAT_NODEKIT=OFF -DOSGEARTH_BUILD_PROCEDURAL_NODEKIT=ON

    - name: 'Upload cmake configure log artifact'
      uses: actions/upload-artifact@v4
      if: ${{ failure() }}
      with:
        name: cmake-configure-log
        path: |
          ${{ runner.workspace }}/build/CMakeCache.txt
        retention-days: 1

    - name: Build
      working-directory: ${{ runner.workspace }}/build
      shell: bash
      run: cmake --build . --parallel 4 --config $BUILD_TYPE

    - name: 'Upload cmake build log artifact'
      uses: actions/upload-artifact@v4
      if: ${{ failure() }}
      with:
        name: cmake-build-log
        path: |
          ${{ runner.workspace }}/build/CMakeCache.txt
        retention-days: 1
