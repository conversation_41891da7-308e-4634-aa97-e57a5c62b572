name: windows-external-project

on: [push, workflow_dispatch]

env:
  VCPKG_BINARY_SOURCES : "clear;x-gha,readwrite"
  VCPKG_VERSION: 'master'

jobs:

  build:
    runs-on: ${{ matrix.os }}

    strategy:
      matrix:
        os: ['windows-2019']
        include:
          - os: 'windows-2019'
            triplet: 'x64-windows'
            mono: ''
            VCPKG_WORKSPACE: 'c:/vcpkg_own'

    steps:
    - uses: actions/checkout@v3
      with:
        submodules: recursive

    - name: Export GitHub Actions cache environment variables
      uses: actions/github-script@v7
      with:
        script: |
          core.exportVariable('ACTIONS_CACHE_URL', process.env.ACTIONS_CACHE_URL || '');
          core.exportVariable('ACTIONS_RUNTIME_TOKEN', process.env.ACTIONS_RUNTIME_TOKEN || '');

    - name: Cloning vcpkg main branch (windows)
      shell: 'bash'
      run: |
        cmake -E make_directory ${{ matrix.VCPKG_WORKSPACE }}
        cd ${{ matrix.VCPKG_WORKSPACE }}
        git clone https://github.com/microsoft/vcpkg
        ./vcpkg/bootstrap-vcpkg.bat -disableMetrics
        echo "set(VCPKG_BUILD_TYPE release)" >> ./vcpkg/triplets/x64-windows.cmake
        echo "set(osg_OPENGL_PROFILE GL3)" >> ./vcpkg/triplets/x64-windows.cmake
        ${{ matrix.VCPKG_WORKSPACE }}/vcpkg/vcpkg version

    - name: Create Build Environment
      run: |
        cmake -E make_directory ${{runner.workspace}}/build

    - name: Configure CMake
      shell: bash
      working-directory: ${{ runner.workspace }}/build
      run: |
        cmake $GITHUB_WORKSPACE/share/ExternalProject -DCMAKE_BUILD_TYPE=release -DCMAKE_TOOLCHAIN_FILE=${{ matrix.VCPKG_WORKSPACE }}/vcpkg/scripts/buildsystems/vcpkg.cmake -DVCPKG_MANIFEST_DIR=$GITHUB_WORKSPACE/share/ExternalProject -DVCPKG_OVERLAY_PORTS=$GITHUB_WORKSPACE/share/ExternalProject/ports

    - name: Build
      working-directory: ${{ runner.workspace }}/build
      shell: bash
      run: cmake --build . --config release
