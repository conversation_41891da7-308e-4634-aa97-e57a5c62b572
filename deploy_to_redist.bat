@echo off
echo 正在发布osgEarth和osgearth_myviewer到redist_desk目录...

REM 创建发布目录结构
if not exist "redist_desk" mkdir "redist_desk"
if not exist "redist_desk\bin" mkdir "redist_desk\bin"
if not exist "redist_desk\lib" mkdir "redist_desk\lib"
if not exist "redist_desk\data" mkdir "redist_desk\data"
if not exist "redist_desk\config" mkdir "redist_desk\config"

REM 复制可执行文件
echo 复制可执行文件...
copy "build_desk\src\applications\osgearth_myviewer\Release\osgearth_myviewer.exe" "redist_desk\bin\" 2>nul
if exist "build_desk\src\applications\osgearth_viewer\Release\osgearth_viewer.exe" (
    copy "build_desk\src\applications\osgearth_viewer\Release\osgearth_viewer.exe" "redist_desk\bin\" 2>nul
)

REM 复制osgEarth库文件
echo 复制osgEarth库文件...
copy "build_desk\src\osgEarth\Release\osgEarth.dll" "redist_desk\bin\" 2>nul
copy "build_desk\src\osgEarth\Release\osgEarth.lib" "redist_desk\lib\" 2>nul

REM 复制osgEarthUtil库文件
if exist "build_desk\src\osgEarthUtil\Release\osgEarthUtil.dll" (
    copy "build_desk\src\osgEarthUtil\Release\osgEarthUtil.dll" "redist_desk\bin\" 2>nul
    copy "build_desk\src\osgEarthUtil\Release\osgEarthUtil.lib" "redist_desk\lib\" 2>nul
)

REM 复制其他osgEarth相关库
for %%f in (build_desk\src\osgEarth*\Release\*.dll) do (
    copy "%%f" "redist_desk\bin\" 2>nul
)

REM 复制OSG依赖库（从vcpkg）
echo 复制OSG依赖库...
if exist "C:\dev\vcpkg\installed\x64-windows\bin\osg*.dll" (
    copy "C:\dev\vcpkg\installed\x64-windows\bin\osg*.dll" "redist_desk\bin\" 2>nul
)
if exist "C:\dev\vcpkg\installed\x64-windows\bin\ot*.dll" (
    copy "C:\dev\vcpkg\installed\x64-windows\bin\ot*.dll" "redist_desk\bin\" 2>nul
)

REM 复制其他必要的依赖库
echo 复制其他依赖库...
copy "C:\dev\vcpkg\installed\x64-windows\bin\curl*.dll" "redist_desk\bin\" 2>nul
copy "C:\dev\vcpkg\installed\x64-windows\bin\geos*.dll" "redist_desk\bin\" 2>nul
copy "C:\dev\vcpkg\installed\x64-windows\bin\zlib*.dll" "redist_desk\bin\" 2>nul
copy "C:\dev\vcpkg\installed\x64-windows\bin\libpng*.dll" "redist_desk\bin\" 2>nul
copy "C:\dev\vcpkg\installed\x64-windows\bin\jpeg*.dll" "redist_desk\bin\" 2>nul
copy "C:\dev\vcpkg\installed\x64-windows\bin\tiff*.dll" "redist_desk\bin\" 2>nul
copy "C:\dev\vcpkg\installed\x64-windows\bin\freetype*.dll" "redist_desk\bin\" 2>nul

REM 复制数据文件
echo 复制数据文件...
if exist "data" (
    xcopy "data\*" "redist_desk\data\" /E /I /Y 2>nul
)

REM 复制配置文件
echo 复制配置文件...
if exist "*.earth" (
    copy "*.earth" "redist_desk\config\" 2>nul
)

REM 创建启动脚本
echo 创建启动脚本...
echo @echo off > "redist_desk\run_myviewer.bat"
echo cd /d "%%~dp0" >> "redist_desk\run_myviewer.bat"
echo set PATH=%%PATH%%;%%~dp0bin >> "redist_desk\run_myviewer.bat"
echo bin\osgearth_myviewer.exe %%* >> "redist_desk\run_myviewer.bat"
echo pause >> "redist_desk\run_myviewer.bat"

REM 创建README文件
echo 创建README文件...
echo osgEarth简化版发布包 > "redist_desk\README.txt"
echo. >> "redist_desk\README.txt"
echo 使用方法: >> "redist_desk\README.txt"
echo 1. 双击 run_myviewer.bat 启动自定义查看器 >> "redist_desk\README.txt"
echo 2. 或者在命令行中运行: bin\osgearth_myviewer.exe >> "redist_desk\README.txt"
echo. >> "redist_desk\README.txt"
echo 功能特性: >> "redist_desk\README.txt"
echo - 谷歌卫星影像显示 >> "redist_desk\README.txt"
echo - AWS Terrarium高程数据 >> "redist_desk\README.txt"
echo - 实时坐标显示 >> "redist_desk\README.txt"
echo - 基本地图操作（缩放、平移、旋转） >> "redist_desk\README.txt"
echo. >> "redist_desk\README.txt"
echo 控制说明: >> "redist_desk\README.txt"
echo - 鼠标左键拖拽: 旋转视角 >> "redist_desk\README.txt"
echo - 鼠标右键拖拽: 缩放 >> "redist_desk\README.txt"
echo - 鼠标中键拖拽: 平移 >> "redist_desk\README.txt"
echo - 's'键: 显示统计信息 >> "redist_desk\README.txt"
echo - 'w'键: 线框模式 >> "redist_desk\README.txt"

echo.
echo 发布完成！文件已复制到 redist_desk 目录
echo 可以运行 redist_desk\run_myviewer.bat 来测试应用程序
pause
