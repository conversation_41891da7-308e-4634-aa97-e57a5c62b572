Detecting compiler hash for triplet x64-windows...
-- Automatically setting %HTTP(S)_PROXY% environment variables to "127.0.0.1:10809".
Compiler found: C:/Program Files/Microsoft Visual Studio/2022/Enterprise/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/cl.exe
The following packages are already installed:
  * abseil:x64-windows@20250127.1#1
  * asmjit:x64-windows@2025-01-22
    blend2d[core,jit]:x64-windows@2025-03-08
  * boost-assert:x64-windows@1.88.0
  * boost-cmake:x64-windows@1.88.0
  * boost-config:x64-windows@1.88.0
  * boost-core:x64-windows@1.88.0
  * boost-headers:x64-windows@1.88.0
  * boost-smart-ptr:x64-windows@1.88.0
  * boost-static-assert:x64-windows@1.88.0
  * boost-throw-exception:x64-windows@1.88.0
  * boost-uninstall:x64-windows@1.88.0
  * brotli:x64-windows@1.1.0#1
  * bzip2[core,tool]:x64-windows@1.0.8#6
  * curl[core,non-http,schannel,ssl,sspi]:x64-windows@8.14.1
  * dirent:x64-windows@1.25
    draco:x64-windows@1.5.7
  * egl-registry:x64-windows@2024-01-25
  * expat:x64-windows@2.7.1
  * fmt:x64-windows@11.0.2#1
  * fontconfig:x64-windows@2.15.0#4
  * freetype[brotli,bzip2,core,png,zlib]:x64-windows@2.13.3
  * freexl:x64-windows@2.0.0#1
  * gdal[core,curl,expat,geos,gif,hdf5,iconv,jpeg,lerc,libkml,libspatialite,libxml2,lzma,netcdf,openjpeg,openssl,pcre2,png,postgresql,qhull,recommended-features,sqlite3,webp,zstd]:x64-windows@3.11.0#1
    geos:x64-windows@3.13.0#1
  * giflib:x64-windows@5.2.2#2
    glew:x64-windows@2.2.0#6
  * gperf:x64-windows@3.1#7
  * hdf5[core,cpp,szip,zlib]:x64-windows@1.14.6
  * imath:x64-windows@3.1.12
  * jasper:x64-windows@4.2.4#2
  * json-c:x64-windows@0.18-20240915
  * lerc:x64-windows@4.0.4
  * libaec:x64-windows@1.1.3#1
  * libdeflate[compression,core,decompression,gzip,zlib]:x64-windows@1.24
  * libgeotiff:x64-windows@1.7.4
  * libgta:x64-windows@1.0.8#5
  * libiconv:x64-windows@1.18#1
  * libjpeg-turbo:x64-windows@3.1.0#1
  * libkml:x64-windows@1.3.0#13
  * liblzma:x64-windows@5.8.1
  * libpng:x64-windows@1.6.48
  * libpq[core,lz4,openssl,zlib]:x64-windows@16.9
  * libspatialite[core,freexl]:x64-windows@5.1.0#4
  * libsquish:x64-windows@1.15#14
    libwebp[core,libwebpmux,nearlossless,simd,unicode]:x64-windows@1.5.0#1
  * libxml2[core,http,iconv,zlib]:x64-windows@2.13.8#1
    libzip[bzip2,core,default-aes]:x64-windows@1.11.4
  * lz4:x64-windows@1.10.0
    meshoptimizer:x64-windows@0.24
  * minizip:x64-windows@1.3.1#1
  * netcdf-c[core,dap,hdf5,nczarr,netcdf-4]:x64-windows@4.8.1#6
  * nlohmann-json:x64-windows@3.12.0
  * nvtt:x64-windows@2.1.2#9
  * openexr:x64-windows@3.3.4
    opengl:x64-windows@2022-12-04#3
  * opengl-registry:x64-windows@2024-02-10#1
  * openjpeg:x64-windows@2.5.3
  * openssl:x64-windows@3.5.0#1
    osg[core,fontconfig,freetype,nvtt,openexr,plugins]:x64-windows@3.6.5#27
  * pcre2[core,jit,platform-default-features]:x64-windows@10.45
  * pkgconf:x64-windows@2.4.3#1
  * proj[core,net,tiff]:x64-windows@9.6.2
    protobuf:x64-windows@5.29.3#1
  * qhull:x64-windows@8.0.2#5
    rocksdb[core,zlib]:x64-windows@10.2.1
    spdlog:x64-windows@1.15.3
    sqlite3[core,json1,rtree,tool]:x64-windows@3.50.2
  * tiff[core,jpeg,lzma,zip]:x64-windows@4.7.0
    tracy[core,crash-handler]:x64-windows@0.11.1#2
  * uriparser:x64-windows@0.9.8
  * utf8-range:x64-windows@5.29.3
  * vcpkg-boost:x64-windows@2025-03-29
  * vcpkg-cmake:x64-windows@2024-04-23
  * vcpkg-cmake-config:x64-windows@2024-05-23
  * vcpkg-cmake-get-vars:x64-windows@2025-05-29
  * vcpkg-pkgconfig-get-modules:x64-windows@2024-04-03
  * vcpkg-tool-meson:x64-windows@1.8.2
  * zlib:x64-windows@1.3.1
  * zstd:x64-windows@1.5.7
blend2d provides CMake targets:

    find_package(blend2d CONFIG REQUIRED)
    target_link_libraries(main PRIVATE blend2d::blend2d)

draco provides CMake targets:

  # this is heuristically generated, and may not be correct
  find_package(draco CONFIG REQUIRED)
  target_link_libraries(main PRIVATE draco::draco)

draco provides pkg-config modules:

  # Draco geometry de(com)pression library.
  draco

geos provides CMake targets:

  # C API (provides long-term ABI stability)
  find_package(GEOS CONFIG REQUIRED)
  target_link_libraries(main PRIVATE GEOS::geos_c)

  # C++ API (will likely change across versions)
  find_package(GEOS CONFIG REQUIRED)
  target_link_libraries(main PRIVATE GEOS::geos)

geos provides pkg-config modules:

  # Geometry Engine, Open Source - C API
  geos

The package opengl is compatible with built-in CMake targets via CMake v3.7 and prior syntax

    find_package(OpenGL REQUIRED)
    target_link_libraries(main PRIVATE ${OPENGL_LIBRARIES})
    target_include_directories(main PRIVATE ${OPENGL_INCLUDE_DIR})

and the CMake v3.8 and beyond imported target syntax

    find_package(OpenGL REQUIRED)
    target_link_libraries(main PRIVATE OpenGL::GL)

introduction of various components

    find_package(OpenGL REQUIRED COMPONENTS GL      # v3.8
                                            GLU     # v3.8
                                            GLX     # v3.10
                                            EGL     # v3.10
                                            OpenGL) # v3.10

The OpenGL SDK is highly platform dependent and is usually an OS component. It's not realistic to build from source for every platform.

    WINDOWS: is part of the Windows SDK which this package installs.
    LINUX: the SDK may be installed from your distro's repo or from 3rd parties manually. There are too many to count.
    APPLE: consult your distribution vendor on the state of OpenGL support: https://support.apple.com/en-us/HT202823

The package glew is compatible with built-in CMake targets:

    find_package(GLEW REQUIRED)
    target_link_libraries(main PRIVATE GLEW::GLEW)

libwebp provides CMake targets:

    find_package(WebP CONFIG REQUIRED)
    # basic usage
    target_link_libraries(main PRIVATE WebP::webp WebP::webpdecoder WebP::webpdemux)
    # for manipulating the WebP graphics format container (port feature libwebpmux)
    target_link_libraries(main PRIVATE WebP::libwebpmux)
    # for sharp RGB to YUV conversion
    target_link_libraries(main PRIVATE WebP::sharpyuv)

libzip provides CMake targets:

  # this is heuristically generated, and may not be correct
  find_package(libzip CONFIG REQUIRED)
  target_link_libraries(main PRIVATE libzip::zip)

libzip provides pkg-config modules:

  # library for handling zip archives
  libzip

meshoptimizer provides CMake targets:

  # this is heuristically generated, and may not be correct
  find_package(meshoptimizer CONFIG REQUIRED)
  target_link_libraries(main PRIVATE meshoptimizer::meshoptimizer)

sqlite3 provides pkgconfig bindings.
sqlite3 provides CMake targets:

    find_package(unofficial-sqlite3 CONFIG REQUIRED)
    target_link_libraries(main PRIVATE unofficial::sqlite3::sqlite3)

The package osg can be configured to use different OpenGL profiles via a custom triplet file.
Possible values are GLCORE, GL2, GL3, GLES1, GLES2, GLES3 and GLES2+GLES3.
The default value is GL2. If you want use other profiles, please add the following 
code to a custom triplet file.
set(osg_OPENGL_PROFILE GL3)

protobuf provides CMake targets:

  # this is heuristically generated, and may not be correct
  find_package(protobuf CONFIG REQUIRED)
  target_link_libraries(main PRIVATE protobuf::libprotoc protobuf::libprotobuf protobuf::libprotobuf-lite)

protobuf provides pkg-config modules:

  # Google's Data Interchange Format
  protobuf-lite

  # Google's Data Interchange Format
  protobuf

rocksdb provides CMake targets:

  # this is heuristically generated, and may not be correct
  find_package(RocksDB CONFIG REQUIRED)
  target_link_libraries(main PRIVATE RocksDB::rocksdb RocksDB::rocksdb-shared)

rocksdb provides pkg-config modules:

  # An embeddable persistent key-value store for fast storage
  rocksdb

The package spdlog provides CMake targets:

    find_package(spdlog CONFIG REQUIRED)
    target_link_libraries(main PRIVATE spdlog::spdlog)

    # Or use the header-only version
    find_package(spdlog CONFIG REQUIRED)
    target_link_libraries(main PRIVATE spdlog::spdlog_header_only)

tracy provides CMake targets:

  # this is heuristically generated, and may not be correct
  find_package(Tracy CONFIG REQUIRED)
  target_link_libraries(main PRIVATE Tracy::TracyClient)

All requested installations completed successfully in: 4.61 ms
