@echo off

chcp 65001 >nul

echo.
if not exist "build_desk" mkdir "build_desk"
cd build_desk
cmake .. -DCMAKE_BUILD_TYPE=Release -DCMAKE_TOOLCHAIN_FILE=C:/dev/vcpkg/scripts/buildsystems/vcpkg.cmake
if %ERRORLEVEL% neq 0 (
    echo CMake配置失败！
    pause
    exit /b 1
)
cd ..

echo.
cmake --build build_desk --target osgEarth --config Release
if %ERRORLEVEL% neq 0 (
    echo osgEarth库编译失败！
    pause
    exit /b 1
)

echo.
cmake --build build_desk --target osgearth_myviewer --config Release
if %ERRORLEVEL% neq 0 (
    echo osgearth_myviewer编译失败！
    pause
    exit /b 1
)

echo.
call deploy_to_redist.bat

