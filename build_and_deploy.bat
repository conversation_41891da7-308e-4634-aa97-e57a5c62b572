@echo off
echo ========================================
echo osgEarth简化版构建和发布脚本
echo ========================================

REM 设置编码为UTF-8
chcp 65001 >nul

echo.
echo 步骤1: 配置CMake项目...
if not exist "build_desk" mkdir "build_desk"
cd build_desk
cmake .. -DCMAKE_BUILD_TYPE=Release -DCMAKE_TOOLCHAIN_FILE=C:/dev/vcpkg/scripts/buildsystems/vcpkg.cmake
if %ERRORLEVEL% neq 0 (
    echo CMake配置失败！
    pause
    exit /b 1
)
cd ..

echo.
echo 步骤2: 编译osgEarth库...
cmake --build build_desk --target osgEarth --config Release
if %ERRORLEVEL% neq 0 (
    echo osgEarth库编译失败！
    pause
    exit /b 1
)

echo.
echo 步骤3: 编译osgearth_myviewer应用程序...
cmake --build build_desk --target osgearth_myviewer --config Release
if %ERRORLEVEL% neq 0 (
    echo osgearth_myviewer编译失败！
    pause
    exit /b 1
)

echo.
echo 步骤4: 发布到redist_desk目录...
call deploy_to_redist.bat

echo.
echo ========================================
echo 构建和发布完成！
echo ========================================
echo.
echo 可以运行以下命令测试应用程序：
echo   redist_desk\run_myviewer.bat
echo.
echo 或者直接运行：
echo   redist_desk\bin\osgearth_myviewer.exe
echo.
pause
