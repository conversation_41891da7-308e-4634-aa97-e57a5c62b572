name: Linux CMake with VCPKG - not work

on: [workflow_dispatch] # push

env:             
  VCPKG_BINARY_SOURCES : 'clear;nuget,GitHub,readwrite'  
  # Customize the CMake build type here (Release, Debug, RelWithDebInfo, etc.)
  BUILD_TYPE: Release
  VCPKG_VERSION: '2020.11' 

jobs:
  
  build:
    runs-on: ${{ matrix.os }}

    strategy:
      matrix:
        os: ['ubuntu-20.04']
        include:
          - os: 'ubuntu-20.04'
            triplet: 'x64-linux'
            mono: 'mono'
            VCPKG_WORKSPACE: '/home/<USER>/vcpkg_own'

    steps:       
    - name: Free disk space / Setup environment
      # https://github.com/actions/virtual-environments/issues/709
      run: |
        echo "Removing large packages"
        sudo rm -rf "/usr/local/share/boost"
        sudo rm -rf "$AGENT_TOOLSDIRECTORY"
        docker rmi $(docker image ls -aq)      
        sudo apt-get autoremove -y
        sudo apt-get clean
        sudo apt-get install libgl-dev
        
    - uses: actions/checkout@v2
      with:
        submodules: recursive
        ref: 3.1  

    - name: vcpkg cache
      id: vcpkgcache
      uses: actions/cache@v2
      with:
        path: |
          ${{ matrix.VCPKG_WORKSPACE }}
          !${{ matrix.VCPKG_WORKSPACE }}/packages
          !${{ matrix.VCPKG_WORKSPACE }}/buildtrees
          !${{ matrix.VCPKG_WORKSPACE }}/downloads
        key: vcpkg-${{ matrix.triplet }}
    
    - name: Installing vcpkg (windows)
      if: steps.vcpkgcache.outputs.cache-hit != 'true'
      shell: 'bash'
      run: |
        cmake -E make_directory ${{ matrix.VCPKG_WORKSPACE }}
        cd ${{ matrix.VCPKG_WORKSPACE }}
        git clone --depth 1 --branch ${{env.VCPKG_VERSION}} https://github.com/microsoft/vcpkg 
        ./vcpkg/bootstrap-vcpkg.sh -disableMetrics
        ${{ matrix.VCPKG_WORKSPACE }}/vcpkg/vcpkg version
        
    # This step assumes `vcpkg` has been bootstrapped (run `./vcpkg/bootstrap-vcpkg`)
    - name: 'Setup NuGet Credentials'
      working-directory: ${{ matrix.VCPKG_WORKSPACE }}
      shell: 'bash'
      run: >
        ${{ matrix.mono }} `./vcpkg/vcpkg fetch nuget | tail -n 1`
        sources add
        -source "https://nuget.pkg.github.com/${{ github.actor }}/index.json"
        -storepasswordincleartext
        -name "GitHub"
        -username "${{ github.actor }}"
        -password "${{ secrets.GITHUB_TOKEN }}"
        
    # Omit this step if you're using manifests
    - name: 'vcpkg package restore'
      working-directory: ${{ matrix.VCPKG_WORKSPACE }}
      shell: 'bash'
      run: >
        ./vcpkg/vcpkg install osg sqlite3 protobuf curl gdal --triplet ${{ matrix.triplet }} 
        
    - name: 'Upload library build log artifact'
      uses: actions/upload-artifact@v2
      if: ${{ failure() }}
      with:
        name: osg-log
        path: |
          ${{ matrix.VCPKG_WORKSPACE }}/vcpkg/buildtrees/osg/config-x64-linux-dbg-out.log
          ${{ matrix.VCPKG_WORKSPACE }}/vcpkg/buildtrees/osg/config-x64-linux-dbg-err.log
          ${{ matrix.VCPKG_WORKSPACE }}/vcpkg/buildtrees/osg/install-x64-linux-dbg-out.log
          ${{ matrix.VCPKG_WORKSPACE }}/vcpkg/buildtrees/osg/install-x64-linux-dbg-err.log
        retention-days: 1
             
    - name: Create Build Environment
      run: |
        cmake -E make_directory ${{runner.workspace}}/build 
    - name: Configure CMake
      shell: bash
      working-directory: ${{ runner.workspace }}/build
      run: cmake $GITHUB_WORKSPACE -DCMAKE_BUILD_TYPE=$BUILD_TYPE -DCMAKE_TOOLCHAIN_FILE=${{ matrix.VCPKG_WORKSPACE }}/vcpkg/scripts/buildsystems/vcpkg.cmake

    - name: 'Upload cmake configure log artifact'
      uses: actions/upload-artifact@v2
      if: ${{ failure() }}
      with:
        name: cmake-configure-log
        path: |
          ${{ runner.workspace }}/build/CMakeCache.txt
        retention-days: 1

    - name: Build
      working-directory: ${{ runner.workspace }}/build
      shell: bash
      run: cmake --build . --parallel 4 --config $BUILD_TYPE 

    - name: 'Upload cmake build log artifact'
      uses: actions/upload-artifact@v2
      if: ${{ failure() }}
      with:
        name: cmake-build-log
        path: |
          ${{ runner.workspace }}/build/CMakeCache.txt
        retention-days: 1
