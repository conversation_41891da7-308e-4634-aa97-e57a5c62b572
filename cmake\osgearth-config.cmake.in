#
# osgEarth CMake Config
#
# Developer inputs:
#    OSGEARTH_VERSION : osgEarth version string
#    OSGEARTH_COMPONENTS : list of libraries for which to create namespaced imports
#    OSGEARTH_PUBLIC_DEPENDENCIES : list of libraries for which to generate find_dependency
#
# User outputs:
#    osgEarth::osgEarth import library (and one for each additonal nodekit)
#    osgEarth_FOUND : true if successful
#    osgEarth_INCLUDE_DIR : include root
#    osgEarth_SHARE_DIR : location of share folder containing cmake files and external resources
#
cmake_minimum_required(VERSION 3.11.0)

@PACKAGE_INIT@

set(osgEarth_VERSION @OSGEARTH_VERSION@)
set(OSGEARTH_VERSION @OSGEARTH_VERSION@)

if(NOT CMAKE_CXX_STANDARD)
    set(CMAKE_CXX_STANDARD 14)
    set(CMAKE_CXX_STANDARD_REQUIRED ON)
    set(CMAKE_CXX_EXTENSIONS OFF)
endif()

set_and_check(osgEarth_INCLUDE_DIR "@PACKAGE_INCLUDE_INSTALL_DIR@")
set(osgEarth_SHARE_DIR "@PACKAGE_OSGEARTH_INSTALL_DATADIR@")

# always depend on the public-facing OSG libraries
include(CMakeFindDependencyMacro)
find_dependency(OpenSceneGraph REQUIRED COMPONENTS osg osgDB osgGA osgUtil osgViewer OpenThreads)

# additional public dependencies
foreach(MY_DEPENDENCY @OSGEARTH_PUBLIC_DEPENDENCIES@)
    find_dependency(${MY_DEPENDENCY} REQUIRED)
endforeach()
if(UNIX)
    find_dependency(Threads REQUIRED)
endif()

# include the target for each library in OSGEARTH_COMPONENTS:
foreach(MY_COMPONENT @OSGEARTH_COMPONENTS@)
    if(NOT TARGET osgEarth::${MY_COMPONENT})
        include("${CMAKE_CURRENT_LIST_DIR}/${MY_COMPONENT}-targets.cmake")
    endif()
endforeach()

target_include_directories(osgEarth::osgEarth INTERFACE "${OPENSCENEGRAPH_INCLUDE_DIR}")

set(osgEarth_FOUND TRUE)
set(OSGEARTH_FOUND TRUE)
