^F:\CMO-DEV\MY_OSGEARTH_WEB\OSGEARTH_ORIGIN\OSGEARTH-SIMP-QIU\BUILD_DESK\CMAKEFILES\0AFA7917D62789D6A5349F6F7A4D0C3D\VECTOR_TILE.PB.H.RULE
setlocal
..\..\vcpkg_installed\x64-windows\tools\protobuf\protoc.exe --cpp_out :F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/src/osgEarth -I F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/src/osgEarth F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/src/osgEarth/vector_tile.proto
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^F:\CMO-DEV\MY_OSGEARTH_WEB\OSGEARTH_ORIGIN\OSGEARTH-SIMP-QIU\BUILD_DESK\CMAKEFILES\0AFA7917D62789D6A5349F6F7A4D0C3D\GLYPHS.PB.H.RULE
setlocal
..\..\vcpkg_installed\x64-windows\tools\protobuf\protoc.exe --cpp_out :F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/src/osgEarth -I F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/src/osgEarth F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/src/osgEarth/glyphs.proto
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^F:\CMO-DEV\MY_OSGEARTH_WEB\OSGEARTH_ORIGIN\OSGEARTH-SIMP-QIU\BUILD_DESK\CMAKEFILES\0AFA7917D62789D6A5349F6F7A4D0C3D\AUTOGENSHADERS.CPP.RULE
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -P F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/src/osgEarth/ConfigureShaders.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^F:\CMO-DEV\MY_OSGEARTH_WEB\OSGEARTH_ORIGIN\OSGEARTH-SIMP-QIU\SRC\OSGEARTH\CMAKELISTS.TXT
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SF:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu -BF:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk --check-stamp-file F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/src/osgEarth/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
