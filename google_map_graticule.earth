<!--
osgEarth Sample - Google Maps with Graticule
Demonstrates loading Google satellite imagery and OpenStreetMap with coordinate grid
-->

<map name="Google Maps with Graticule">

    <!-- Google Satellite Imagery -->
    <XYZImage name="Google Satellite">
        <url>http://mt1.google.com/vt/lyrs=s&amp;x={x}&amp;y={y}&amp;z={z}</url>
        <profile>spherical-mercator</profile>
        <cache_policy usage="cache_first"/>
    </XYZImage>

    <!-- OpenStreetMap as secondary layer -->
    <XYZImage name="OpenStreetMap" enabled="false">
        <url>http://tile.openstreetmap.org/{z}/{x}/{y}.png</url>
        <profile>spherical-mercator</profile>
        <cache_policy usage="cache_first"/>
    </XYZImage>

    <!-- Geodetic Graticule (coordinate grid) -->
    <GeodeticGraticule name="Coordinate Grid">
        <color>#ffff007f</color>
        <label_color>#ffffffff</label_color>
        <grid_lines>10</grid_lines>
        <resolutions>10 5 2.5 1.0 0.5 0.25 0.125 0.0625 0.3125</resolutions>
        <grid_lines_visible>true</grid_lines_visible>
        <grid_labels_visible>true</grid_labels_visible>
        <edge_labels_visible>true</edge_labels_visible>
    </GeodeticGraticule>

    <!-- Viewpoints for easy navigation -->
    <viewpoints>
        <viewpoint name="Beijing" heading="0" height="0" lat="39.9042" long="116.4074" pitch="-45" range="100000"/>
        <viewpoint name="New York" heading="0" height="0" lat="40.7128" long="-74.0060" pitch="-45" range="100000"/>
        <viewpoint name="London" heading="0" height="0" lat="51.5074" long="-0.1278" pitch="-45" range="100000"/>
        <viewpoint name="Tokyo" heading="0" height="0" lat="35.6762" long="139.6503" pitch="-45" range="100000"/>
    </viewpoints>

</map>
