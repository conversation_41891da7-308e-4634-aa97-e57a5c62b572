﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{59F5F234-9F89-3B79-AB4B-2E21EC3563DF}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <VcpkgEnabled>false</VcpkgEnabled>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>osgEarth</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="do_not_import_user.props" Condition="exists('do_not_import_user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarth\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">osgEarth.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">osgEarthd</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarth\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">osgEarth.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">osgEarth</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarth\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">osgEarth.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">osgEarths</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarth\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">osgEarth.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">osgEarth</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</GenerateManifest>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarth;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/include" /external:I "F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/include/geos" /bigobj /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>4251;4275</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp14</LanguageStandard>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;_SCL_SECURE_NO_WARNINGS;_CRT_SECURE_NO_DEPRECATE;OSGEARTH_LIBRARY;PROTOBUF_USE_DLLS;ABSL_CONSUME_DLL;SPDLOG_SHARED_LIB;SPDLOG_COMPILED_LIB;SPDLOG_FMT_EXTERNAL;FMT_SHARED;MESHOPTIMIZER_API=__declspec(dllimport);GDAL_DEBUG;CMAKE_INTDIR="Debug";osgEarth_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;_SCL_SECURE_NO_WARNINGS;_CRT_SECURE_NO_DEPRECATE;OSGEARTH_LIBRARY;PROTOBUF_USE_DLLS;ABSL_CONSUME_DLL;SPDLOG_SHARED_LIB;SPDLOG_COMPILED_LIB;SPDLOG_FMT_EXTERNAL;FMT_SHARED;MESHOPTIMIZER_API=__declspec(dllimport);GDAL_DEBUG;CMAKE_INTDIR=\"Debug\";osgEarth_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarth;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\include;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\include\geos;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarth;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\include;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\include\geos;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message></Message>
      <Command>setlocal
"C:\Program Files\PowerShell\7\pwsh.exe" -noprofile -executionpolicy Bypass -file C:/dev/vcpkg/scripts/buildsystems/msbuild/applocal.ps1 -targetBinary F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/src/osgEarth/Debug/osgEarthd.dll -installedDir F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/debug/bin -OutVariable out
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>..\..\vcpkg_installed\x64-windows\debug\lib\osgManipulatord.lib;..\..\vcpkg_installed\x64-windows\debug\lib\osgShadowd.lib;..\..\vcpkg_installed\x64-windows\debug\lib\osgSimd.lib;..\..\vcpkg_installed\x64-windows\debug\lib\osgViewerd.lib;..\..\vcpkg_installed\x64-windows\debug\lib\osgGAd.lib;..\..\vcpkg_installed\x64-windows\debug\lib\osgUtild.lib;..\..\vcpkg_installed\x64-windows\debug\lib\osgTextd.lib;..\..\vcpkg_installed\x64-windows\debug\lib\osgDBd.lib;..\..\vcpkg_installed\x64-windows\debug\lib\osgd.lib;..\..\vcpkg_installed\x64-windows\debug\lib\OpenThreadsd.lib;..\..\vcpkg_installed\x64-windows\debug\lib\libprotobufd.lib;..\..\vcpkg_installed\x64-windows\debug\lib\spdlogd.lib;..\..\vcpkg_installed\x64-windows\debug\lib\sqlite3.lib;..\..\vcpkg_installed\x64-windows\debug\lib\blend2d.lib;..\..\vcpkg_installed\x64-windows\debug\lib\meshoptimizer.lib;..\..\vcpkg_installed\x64-windows\debug\lib\osgManipulatord.lib;..\..\vcpkg_installed\x64-windows\debug\lib\osgShadowd.lib;..\..\vcpkg_installed\x64-windows\debug\lib\osgSimd.lib;..\..\vcpkg_installed\x64-windows\debug\lib\osgViewerd.lib;..\..\vcpkg_installed\x64-windows\debug\lib\osgGAd.lib;..\..\vcpkg_installed\x64-windows\debug\lib\osgUtild.lib;..\..\vcpkg_installed\x64-windows\debug\lib\osgTextd.lib;..\..\vcpkg_installed\x64-windows\debug\lib\osgDBd.lib;..\..\vcpkg_installed\x64-windows\debug\lib\osgd.lib;..\..\vcpkg_installed\x64-windows\debug\lib\OpenThreadsd.lib;opengl32.lib;..\..\vcpkg_installed\x64-windows\debug\lib\gdald.lib;..\..\vcpkg_installed\x64-windows\debug\lib\geos_c.lib;..\..\vcpkg_installed\x64-windows\debug\lib\sqlite3.lib;..\..\vcpkg_installed\x64-windows\debug\lib\abseil_dll.lib;..\..\vcpkg_installed\x64-windows\debug\lib\fmtd.lib;..\..\vcpkg_installed\x64-windows\debug\lib\libcurl-d.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/src/osgEarth/Debug/osgEarthd.lib</ImportLibrary>
      <ProgramDataBaseFile>F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/src/osgEarth/Debug/osgEarthd.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarth;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/include" /external:I "F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/include/geos" /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <DisableSpecificWarnings>4251;4275</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp14</LanguageStandard>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_SCL_SECURE_NO_WARNINGS;_CRT_SECURE_NO_DEPRECATE;OSGEARTH_LIBRARY;PROTOBUF_USE_DLLS;ABSL_CONSUME_DLL;SPDLOG_SHARED_LIB;SPDLOG_COMPILED_LIB;SPDLOG_FMT_EXTERNAL;FMT_SHARED;MESHOPTIMIZER_API=__declspec(dllimport);CMAKE_INTDIR="Release";osgEarth_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_SCL_SECURE_NO_WARNINGS;_CRT_SECURE_NO_DEPRECATE;OSGEARTH_LIBRARY;PROTOBUF_USE_DLLS;ABSL_CONSUME_DLL;SPDLOG_SHARED_LIB;SPDLOG_COMPILED_LIB;SPDLOG_FMT_EXTERNAL;FMT_SHARED;MESHOPTIMIZER_API=__declspec(dllimport);CMAKE_INTDIR=\"Release\";osgEarth_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarth;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\include;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\include\geos;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarth;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\include;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\include\geos;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message></Message>
      <Command>setlocal
"C:\Program Files\PowerShell\7\pwsh.exe" -noprofile -executionpolicy Bypass -file C:/dev/vcpkg/scripts/buildsystems/msbuild/applocal.ps1 -targetBinary F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/src/osgEarth/Release/osgEarth.dll -installedDir F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/bin -OutVariable out
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>..\..\vcpkg_installed\x64-windows\lib\osgManipulator.lib;..\..\vcpkg_installed\x64-windows\lib\osgShadow.lib;..\..\vcpkg_installed\x64-windows\lib\osgSim.lib;..\..\vcpkg_installed\x64-windows\lib\osgViewer.lib;..\..\vcpkg_installed\x64-windows\lib\osgGA.lib;..\..\vcpkg_installed\x64-windows\lib\osgUtil.lib;..\..\vcpkg_installed\x64-windows\lib\osgText.lib;..\..\vcpkg_installed\x64-windows\lib\osgDB.lib;..\..\vcpkg_installed\x64-windows\lib\osg.lib;..\..\vcpkg_installed\x64-windows\lib\OpenThreads.lib;..\..\vcpkg_installed\x64-windows\lib\libprotobuf.lib;..\..\vcpkg_installed\x64-windows\lib\spdlog.lib;..\..\vcpkg_installed\x64-windows\debug\lib\sqlite3.lib;..\..\vcpkg_installed\x64-windows\lib\blend2d.lib;..\..\vcpkg_installed\x64-windows\lib\meshoptimizer.lib;..\..\vcpkg_installed\x64-windows\lib\osgManipulator.lib;..\..\vcpkg_installed\x64-windows\lib\osgShadow.lib;..\..\vcpkg_installed\x64-windows\lib\osgSim.lib;..\..\vcpkg_installed\x64-windows\lib\osgViewer.lib;..\..\vcpkg_installed\x64-windows\lib\osgGA.lib;..\..\vcpkg_installed\x64-windows\lib\osgUtil.lib;..\..\vcpkg_installed\x64-windows\lib\osgText.lib;..\..\vcpkg_installed\x64-windows\lib\osgDB.lib;..\..\vcpkg_installed\x64-windows\lib\osg.lib;..\..\vcpkg_installed\x64-windows\lib\OpenThreads.lib;opengl32.lib;..\..\vcpkg_installed\x64-windows\lib\gdal.lib;..\..\vcpkg_installed\x64-windows\lib\geos_c.lib;..\..\vcpkg_installed\x64-windows\debug\lib\sqlite3.lib;..\..\vcpkg_installed\x64-windows\lib\abseil_dll.lib;..\..\vcpkg_installed\x64-windows\lib\fmt.lib;..\..\vcpkg_installed\x64-windows\lib\libcurl.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/src/osgEarth/Release/osgEarth.lib</ImportLibrary>
      <ProgramDataBaseFile>F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/src/osgEarth/Release/osgEarth.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarth;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/include" /external:I "F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/include/geos" /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <DisableSpecificWarnings>4251;4275</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp14</LanguageStandard>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_SCL_SECURE_NO_WARNINGS;_CRT_SECURE_NO_DEPRECATE;OSGEARTH_LIBRARY;PROTOBUF_USE_DLLS;ABSL_CONSUME_DLL;SPDLOG_SHARED_LIB;SPDLOG_COMPILED_LIB;SPDLOG_FMT_EXTERNAL;FMT_SHARED;MESHOPTIMIZER_API=__declspec(dllimport);CMAKE_INTDIR="MinSizeRel";osgEarth_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_SCL_SECURE_NO_WARNINGS;_CRT_SECURE_NO_DEPRECATE;OSGEARTH_LIBRARY;PROTOBUF_USE_DLLS;ABSL_CONSUME_DLL;SPDLOG_SHARED_LIB;SPDLOG_COMPILED_LIB;SPDLOG_FMT_EXTERNAL;FMT_SHARED;MESHOPTIMIZER_API=__declspec(dllimport);CMAKE_INTDIR=\"MinSizeRel\";osgEarth_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarth;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\include;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\include\geos;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarth;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\include;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\include\geos;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message></Message>
      <Command>setlocal
"C:\Program Files\PowerShell\7\pwsh.exe" -noprofile -executionpolicy Bypass -file C:/dev/vcpkg/scripts/buildsystems/msbuild/applocal.ps1 -targetBinary F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/src/osgEarth/MinSizeRel/osgEarths.dll -installedDir F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/bin -OutVariable out
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>..\..\vcpkg_installed\x64-windows\lib\osgManipulator.lib;..\..\vcpkg_installed\x64-windows\lib\osgShadow.lib;..\..\vcpkg_installed\x64-windows\lib\osgSim.lib;..\..\vcpkg_installed\x64-windows\lib\osgViewer.lib;..\..\vcpkg_installed\x64-windows\lib\osgGA.lib;..\..\vcpkg_installed\x64-windows\lib\osgUtil.lib;..\..\vcpkg_installed\x64-windows\lib\osgText.lib;..\..\vcpkg_installed\x64-windows\lib\osgDB.lib;..\..\vcpkg_installed\x64-windows\lib\osg.lib;..\..\vcpkg_installed\x64-windows\lib\OpenThreads.lib;..\..\vcpkg_installed\x64-windows\lib\libprotobuf.lib;..\..\vcpkg_installed\x64-windows\lib\spdlog.lib;..\..\vcpkg_installed\x64-windows\debug\lib\sqlite3.lib;..\..\vcpkg_installed\x64-windows\lib\blend2d.lib;..\..\vcpkg_installed\x64-windows\lib\meshoptimizer.lib;..\..\vcpkg_installed\x64-windows\lib\osgManipulator.lib;..\..\vcpkg_installed\x64-windows\lib\osgShadow.lib;..\..\vcpkg_installed\x64-windows\lib\osgSim.lib;..\..\vcpkg_installed\x64-windows\lib\osgViewer.lib;..\..\vcpkg_installed\x64-windows\lib\osgGA.lib;..\..\vcpkg_installed\x64-windows\lib\osgUtil.lib;..\..\vcpkg_installed\x64-windows\lib\osgText.lib;..\..\vcpkg_installed\x64-windows\lib\osgDB.lib;..\..\vcpkg_installed\x64-windows\lib\osg.lib;..\..\vcpkg_installed\x64-windows\lib\OpenThreads.lib;opengl32.lib;..\..\vcpkg_installed\x64-windows\lib\gdal.lib;..\..\vcpkg_installed\x64-windows\lib\geos_c.lib;..\..\vcpkg_installed\x64-windows\debug\lib\sqlite3.lib;..\..\vcpkg_installed\x64-windows\lib\abseil_dll.lib;..\..\vcpkg_installed\x64-windows\lib\fmt.lib;..\..\vcpkg_installed\x64-windows\lib\libcurl.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/src/osgEarth/MinSizeRel/osgEarths.lib</ImportLibrary>
      <ProgramDataBaseFile>F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/src/osgEarth/MinSizeRel/osgEarths.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarth;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/include" /external:I "F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/include/geos" /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>4251;4275</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp14</LanguageStandard>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_SCL_SECURE_NO_WARNINGS;_CRT_SECURE_NO_DEPRECATE;OSGEARTH_LIBRARY;PROTOBUF_USE_DLLS;ABSL_CONSUME_DLL;SPDLOG_SHARED_LIB;SPDLOG_COMPILED_LIB;SPDLOG_FMT_EXTERNAL;FMT_SHARED;MESHOPTIMIZER_API=__declspec(dllimport);CMAKE_INTDIR="RelWithDebInfo";osgEarth_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_SCL_SECURE_NO_WARNINGS;_CRT_SECURE_NO_DEPRECATE;OSGEARTH_LIBRARY;PROTOBUF_USE_DLLS;ABSL_CONSUME_DLL;SPDLOG_SHARED_LIB;SPDLOG_COMPILED_LIB;SPDLOG_FMT_EXTERNAL;FMT_SHARED;MESHOPTIMIZER_API=__declspec(dllimport);CMAKE_INTDIR=\"RelWithDebInfo\";osgEarth_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarth;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\include;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\include\geos;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarth;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\include;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\include\geos;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message></Message>
      <Command>setlocal
"C:\Program Files\PowerShell\7\pwsh.exe" -noprofile -executionpolicy Bypass -file C:/dev/vcpkg/scripts/buildsystems/msbuild/applocal.ps1 -targetBinary F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/src/osgEarth/RelWithDebInfo/osgEarth.dll -installedDir F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/vcpkg_installed/x64-windows/bin -OutVariable out
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>..\..\vcpkg_installed\x64-windows\lib\osgManipulator.lib;..\..\vcpkg_installed\x64-windows\lib\osgShadow.lib;..\..\vcpkg_installed\x64-windows\lib\osgSim.lib;..\..\vcpkg_installed\x64-windows\lib\osgViewer.lib;..\..\vcpkg_installed\x64-windows\lib\osgGA.lib;..\..\vcpkg_installed\x64-windows\lib\osgUtil.lib;..\..\vcpkg_installed\x64-windows\lib\osgText.lib;..\..\vcpkg_installed\x64-windows\lib\osgDB.lib;..\..\vcpkg_installed\x64-windows\lib\osg.lib;..\..\vcpkg_installed\x64-windows\lib\OpenThreads.lib;..\..\vcpkg_installed\x64-windows\lib\libprotobuf.lib;..\..\vcpkg_installed\x64-windows\lib\spdlog.lib;..\..\vcpkg_installed\x64-windows\debug\lib\sqlite3.lib;..\..\vcpkg_installed\x64-windows\lib\blend2d.lib;..\..\vcpkg_installed\x64-windows\lib\meshoptimizer.lib;..\..\vcpkg_installed\x64-windows\lib\osgManipulator.lib;..\..\vcpkg_installed\x64-windows\lib\osgShadow.lib;..\..\vcpkg_installed\x64-windows\lib\osgSim.lib;..\..\vcpkg_installed\x64-windows\lib\osgViewer.lib;..\..\vcpkg_installed\x64-windows\lib\osgGA.lib;..\..\vcpkg_installed\x64-windows\lib\osgUtil.lib;..\..\vcpkg_installed\x64-windows\lib\osgText.lib;..\..\vcpkg_installed\x64-windows\lib\osgDB.lib;..\..\vcpkg_installed\x64-windows\lib\osg.lib;..\..\vcpkg_installed\x64-windows\lib\OpenThreads.lib;opengl32.lib;..\..\vcpkg_installed\x64-windows\lib\gdal.lib;..\..\vcpkg_installed\x64-windows\lib\geos_c.lib;..\..\vcpkg_installed\x64-windows\debug\lib\sqlite3.lib;..\..\vcpkg_installed\x64-windows\lib\abseil_dll.lib;..\..\vcpkg_installed\x64-windows\lib\fmt.lib;..\..\vcpkg_installed\x64-windows\lib\libcurl.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/src/osgEarth/RelWithDebInfo/osgEarth.lib</ImportLibrary>
      <ProgramDataBaseFile>F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/src/osgEarth/RelWithDebInfo/osgEarth.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\CMakeFiles\0afa7917d62789d6a5349f6f7a4d0c3d\vector_tile.pb.h.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Running cpp protocol buffer compiler on vector_tile.proto</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
..\..\vcpkg_installed\x64-windows\tools\protobuf\protoc.exe --cpp_out :F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/src/osgEarth -I F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/src/osgEarth F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/src/osgEarth/vector_tile.proto
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\vector_tile.proto;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarth\vector_tile.pb.h;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarth\vector_tile.pb.cc</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Running cpp protocol buffer compiler on vector_tile.proto</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
..\..\vcpkg_installed\x64-windows\tools\protobuf\protoc.exe --cpp_out :F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/src/osgEarth -I F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/src/osgEarth F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/src/osgEarth/vector_tile.proto
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\vector_tile.proto;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarth\vector_tile.pb.h;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarth\vector_tile.pb.cc</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Running cpp protocol buffer compiler on vector_tile.proto</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
..\..\vcpkg_installed\x64-windows\tools\protobuf\protoc.exe --cpp_out :F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/src/osgEarth -I F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/src/osgEarth F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/src/osgEarth/vector_tile.proto
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\vector_tile.proto;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarth\vector_tile.pb.h;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarth\vector_tile.pb.cc</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Running cpp protocol buffer compiler on vector_tile.proto</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
..\..\vcpkg_installed\x64-windows\tools\protobuf\protoc.exe --cpp_out :F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/src/osgEarth -I F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/src/osgEarth F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/src/osgEarth/vector_tile.proto
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\vector_tile.proto;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarth\vector_tile.pb.h;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarth\vector_tile.pb.cc</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\CMakeFiles\0afa7917d62789d6a5349f6f7a4d0c3d\glyphs.pb.h.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Running cpp protocol buffer compiler on glyphs.proto</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
..\..\vcpkg_installed\x64-windows\tools\protobuf\protoc.exe --cpp_out :F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/src/osgEarth -I F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/src/osgEarth F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/src/osgEarth/glyphs.proto
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\glyphs.proto;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarth\glyphs.pb.h;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarth\glyphs.pb.cc</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Running cpp protocol buffer compiler on glyphs.proto</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
..\..\vcpkg_installed\x64-windows\tools\protobuf\protoc.exe --cpp_out :F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/src/osgEarth -I F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/src/osgEarth F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/src/osgEarth/glyphs.proto
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\glyphs.proto;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarth\glyphs.pb.h;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarth\glyphs.pb.cc</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Running cpp protocol buffer compiler on glyphs.proto</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
..\..\vcpkg_installed\x64-windows\tools\protobuf\protoc.exe --cpp_out :F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/src/osgEarth -I F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/src/osgEarth F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/src/osgEarth/glyphs.proto
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\glyphs.proto;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarth\glyphs.pb.h;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarth\glyphs.pb.cc</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Running cpp protocol buffer compiler on glyphs.proto</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
..\..\vcpkg_installed\x64-windows\tools\protobuf\protoc.exe --cpp_out :F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/src/osgEarth -I F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/src/osgEarth F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/src/osgEarth/glyphs.proto
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\glyphs.proto;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarth\glyphs.pb.h;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarth\glyphs.pb.cc</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\CMakeFiles\0afa7917d62789d6a5349f6f7a4d0c3d\AutoGenShaders.cpp.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Generating AutoGenShaders.cpp</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -P F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/src/osgEarth/ConfigureShaders.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CascadeDraping.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Chonk.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Chonk.Culling.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\DepthOffset.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Draping.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\DrawInstancedAttribute.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GPUClamping.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GPUClamping.lib.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\HexTiling.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Instancing.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LineDrawable.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MetadataNode.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\WireLines.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\PhongLighting.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\PointDrawable.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Text.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Text_legacy.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ContourMap.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GeodeticGraticule.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LogDepthBuffer.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LogDepthBuffer.VertOnly.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ShadowCaster.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\SimpleOceanLayer.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\RTTPicker.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\WindLayer.CS.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\PBR.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Shaders.cpp.in;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\cmake\ConfigureShaders.cmake.in;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarth\AutoGenShaders.cpp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Generating AutoGenShaders.cpp</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -P F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/src/osgEarth/ConfigureShaders.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CascadeDraping.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Chonk.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Chonk.Culling.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\DepthOffset.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Draping.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\DrawInstancedAttribute.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GPUClamping.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GPUClamping.lib.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\HexTiling.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Instancing.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LineDrawable.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MetadataNode.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\WireLines.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\PhongLighting.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\PointDrawable.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Text.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Text_legacy.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ContourMap.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GeodeticGraticule.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LogDepthBuffer.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LogDepthBuffer.VertOnly.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ShadowCaster.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\SimpleOceanLayer.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\RTTPicker.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\WindLayer.CS.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\PBR.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Shaders.cpp.in;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\cmake\ConfigureShaders.cmake.in;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarth\AutoGenShaders.cpp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Generating AutoGenShaders.cpp</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -P F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/src/osgEarth/ConfigureShaders.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CascadeDraping.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Chonk.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Chonk.Culling.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\DepthOffset.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Draping.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\DrawInstancedAttribute.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GPUClamping.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GPUClamping.lib.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\HexTiling.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Instancing.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LineDrawable.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MetadataNode.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\WireLines.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\PhongLighting.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\PointDrawable.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Text.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Text_legacy.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ContourMap.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GeodeticGraticule.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LogDepthBuffer.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LogDepthBuffer.VertOnly.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ShadowCaster.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\SimpleOceanLayer.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\RTTPicker.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\WindLayer.CS.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\PBR.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Shaders.cpp.in;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\cmake\ConfigureShaders.cmake.in;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarth\AutoGenShaders.cpp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Generating AutoGenShaders.cpp</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -P F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/src/osgEarth/ConfigureShaders.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CascadeDraping.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Chonk.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Chonk.Culling.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\DepthOffset.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Draping.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\DrawInstancedAttribute.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GPUClamping.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GPUClamping.lib.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\HexTiling.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Instancing.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LineDrawable.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MetadataNode.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\WireLines.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\PhongLighting.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\PointDrawable.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Text.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Text_legacy.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ContourMap.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GeodeticGraticule.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LogDepthBuffer.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LogDepthBuffer.VertOnly.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ShadowCaster.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\SimpleOceanLayer.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\RTTPicker.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\WindLayer.CS.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\PBR.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Shaders.cpp.in;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\cmake\ConfigureShaders.cmake.in;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarth\AutoGenShaders.cpp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/src/osgEarth/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SF:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu -BF:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk --check-stamp-file F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/src/osgEarth/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeParseArguments.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckCSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckIncludeFile.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindSQLite3.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindZLIB.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\SelectLibraryConfigurations.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\absl\abslConfig.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\absl\abslConfigVersion.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\absl\abslTargets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\absl\abslTargets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\absl\abslTargets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\blend2d\blend2d-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\blend2d\blend2d-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\blend2d\blend2d-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\blend2d\blend2d-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\curl\CURLConfig.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\curl\CURLConfigVersion.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\curl\CURLTargets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\curl\CURLTargets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\curl\CURLTargets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\curl\vcpkg-cmake-wrapper.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\fmt\fmt-config-version.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\fmt\fmt-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\fmt\fmt-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\fmt\fmt-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\fmt\fmt-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\gdal\GDAL-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\gdal\GDAL-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\gdal\GDAL-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\gdal\GDALConfig.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\gdal\GDALConfigVersion.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\gdal\vcpkg-cmake-wrapper.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\geos\geos-config-version.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\geos\geos-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\geos\geos-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\geos\geos-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\geos\geos-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\libzip\libzip-config-version.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\libzip\libzip-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\libzip\libzip-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\libzip\libzip-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\libzip\libzip-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\meshoptimizer\meshoptimizerConfig.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\meshoptimizer\meshoptimizerConfigVersion.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\meshoptimizer\meshoptimizerTargets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\meshoptimizer\meshoptimizerTargets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\meshoptimizer\meshoptimizerTargets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-config-version.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-generate.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-module.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-options.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\protobuf\vcpkg-cmake-wrapper.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\spdlog\spdlogConfig.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\spdlog\spdlogConfigTargets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\spdlog\spdlogConfigTargets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\spdlog\spdlogConfigTargets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\spdlog\spdlogConfigVersion.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\utf8_range\utf8_range-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\utf8_range\utf8_range-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\utf8_range\utf8_range-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\utf8_range\utf8_range-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\zlib\vcpkg-cmake-wrapper.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\cmake\ConfigureShaders.cmake.in;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\BuildConfig.in;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Version.in;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarth\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/src/osgEarth/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SF:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu -BF:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk --check-stamp-file F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/src/osgEarth/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeParseArguments.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckCSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckIncludeFile.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindSQLite3.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindZLIB.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\SelectLibraryConfigurations.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\absl\abslConfig.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\absl\abslConfigVersion.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\absl\abslTargets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\absl\abslTargets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\absl\abslTargets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\blend2d\blend2d-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\blend2d\blend2d-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\blend2d\blend2d-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\blend2d\blend2d-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\curl\CURLConfig.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\curl\CURLConfigVersion.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\curl\CURLTargets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\curl\CURLTargets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\curl\CURLTargets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\curl\vcpkg-cmake-wrapper.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\fmt\fmt-config-version.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\fmt\fmt-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\fmt\fmt-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\fmt\fmt-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\fmt\fmt-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\gdal\GDAL-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\gdal\GDAL-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\gdal\GDAL-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\gdal\GDALConfig.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\gdal\GDALConfigVersion.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\gdal\vcpkg-cmake-wrapper.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\geos\geos-config-version.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\geos\geos-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\geos\geos-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\geos\geos-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\geos\geos-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\libzip\libzip-config-version.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\libzip\libzip-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\libzip\libzip-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\libzip\libzip-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\libzip\libzip-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\meshoptimizer\meshoptimizerConfig.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\meshoptimizer\meshoptimizerConfigVersion.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\meshoptimizer\meshoptimizerTargets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\meshoptimizer\meshoptimizerTargets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\meshoptimizer\meshoptimizerTargets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-config-version.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-generate.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-module.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-options.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\protobuf\vcpkg-cmake-wrapper.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\spdlog\spdlogConfig.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\spdlog\spdlogConfigTargets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\spdlog\spdlogConfigTargets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\spdlog\spdlogConfigTargets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\spdlog\spdlogConfigVersion.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\utf8_range\utf8_range-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\utf8_range\utf8_range-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\utf8_range\utf8_range-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\utf8_range\utf8_range-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\zlib\vcpkg-cmake-wrapper.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\cmake\ConfigureShaders.cmake.in;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\BuildConfig.in;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Version.in;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarth\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/src/osgEarth/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SF:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu -BF:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk --check-stamp-file F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/src/osgEarth/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeParseArguments.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckCSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckIncludeFile.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindSQLite3.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindZLIB.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\SelectLibraryConfigurations.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\absl\abslConfig.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\absl\abslConfigVersion.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\absl\abslTargets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\absl\abslTargets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\absl\abslTargets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\blend2d\blend2d-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\blend2d\blend2d-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\blend2d\blend2d-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\blend2d\blend2d-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\curl\CURLConfig.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\curl\CURLConfigVersion.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\curl\CURLTargets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\curl\CURLTargets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\curl\CURLTargets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\curl\vcpkg-cmake-wrapper.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\fmt\fmt-config-version.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\fmt\fmt-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\fmt\fmt-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\fmt\fmt-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\fmt\fmt-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\gdal\GDAL-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\gdal\GDAL-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\gdal\GDAL-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\gdal\GDALConfig.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\gdal\GDALConfigVersion.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\gdal\vcpkg-cmake-wrapper.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\geos\geos-config-version.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\geos\geos-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\geos\geos-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\geos\geos-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\geos\geos-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\libzip\libzip-config-version.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\libzip\libzip-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\libzip\libzip-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\libzip\libzip-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\libzip\libzip-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\meshoptimizer\meshoptimizerConfig.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\meshoptimizer\meshoptimizerConfigVersion.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\meshoptimizer\meshoptimizerTargets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\meshoptimizer\meshoptimizerTargets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\meshoptimizer\meshoptimizerTargets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-config-version.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-generate.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-module.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-options.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\protobuf\vcpkg-cmake-wrapper.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\spdlog\spdlogConfig.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\spdlog\spdlogConfigTargets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\spdlog\spdlogConfigTargets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\spdlog\spdlogConfigTargets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\spdlog\spdlogConfigVersion.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\utf8_range\utf8_range-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\utf8_range\utf8_range-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\utf8_range\utf8_range-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\utf8_range\utf8_range-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\zlib\vcpkg-cmake-wrapper.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\cmake\ConfigureShaders.cmake.in;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\BuildConfig.in;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Version.in;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarth\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/src/osgEarth/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SF:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu -BF:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk --check-stamp-file F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-simp-qiu/build_desk/src/osgEarth/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeParseArguments.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckCSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckIncludeFile.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindSQLite3.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindZLIB.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\SelectLibraryConfigurations.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\absl\abslConfig.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\absl\abslConfigVersion.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\absl\abslTargets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\absl\abslTargets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\absl\abslTargets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\blend2d\blend2d-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\blend2d\blend2d-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\blend2d\blend2d-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\blend2d\blend2d-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\curl\CURLConfig.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\curl\CURLConfigVersion.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\curl\CURLTargets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\curl\CURLTargets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\curl\CURLTargets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\curl\vcpkg-cmake-wrapper.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\fmt\fmt-config-version.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\fmt\fmt-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\fmt\fmt-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\fmt\fmt-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\fmt\fmt-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\gdal\GDAL-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\gdal\GDAL-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\gdal\GDAL-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\gdal\GDALConfig.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\gdal\GDALConfigVersion.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\gdal\vcpkg-cmake-wrapper.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\geos\geos-config-version.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\geos\geos-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\geos\geos-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\geos\geos-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\geos\geos-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\libzip\libzip-config-version.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\libzip\libzip-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\libzip\libzip-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\libzip\libzip-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\libzip\libzip-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\meshoptimizer\meshoptimizerConfig.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\meshoptimizer\meshoptimizerConfigVersion.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\meshoptimizer\meshoptimizerTargets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\meshoptimizer\meshoptimizerTargets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\meshoptimizer\meshoptimizerTargets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-config-version.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-generate.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-module.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-options.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\protobuf\vcpkg-cmake-wrapper.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\spdlog\spdlogConfig.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\spdlog\spdlogConfigTargets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\spdlog\spdlogConfigTargets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\spdlog\spdlogConfigTargets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\spdlog\spdlogConfigVersion.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\utf8_range\utf8_range-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\utf8_range\utf8_range-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\utf8_range\utf8_range-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\utf8_range\utf8_range-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\vcpkg_installed\x64-windows\share\zlib\vcpkg-cmake-wrapper.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\cmake\ConfigureShaders.cmake.in;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\BuildConfig.in;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Version.in;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarth\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\AGG.h" />
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\AltitudeFilter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\AltitudeSymbol">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\AnnotationData">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\AnnotationLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\AnnotationNode">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\AnnotationRegistry">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\AnnotationSettings">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\AnnotationUtils">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ArcGISServer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ArcGISTilePackage">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\AtlasBuilder">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\AttributesFilter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\AutoClipPlaneHandler">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\AutoScaleCallback">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\AzureMaps">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\BboxDrawable">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\BBoxSymbol">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\BillboardResource">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\BillboardSymbol">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Bing">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Bounds">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\BufferFilter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\BuildGeometryFilter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\BuildTextFilter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Cache">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CacheBin">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CachePolicy">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CacheSeed">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Callbacks">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Callouts">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CameraUtils">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Capabilities">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CascadeDrapingDecorator">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CentroidFilter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CesiumIon">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Chonk">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CircleNode">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ClampableNode">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ClampCallback">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Clamping">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ClampingTechnique">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ClipSpace">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ClusterNode">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Color">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ColorFilter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Common">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Composite">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CompressedArray">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CompositeTiledModelLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Config">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Containers">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ContourMap">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ConvertTypeFilter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Coverage">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CoverageLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CoverageSymbol">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CropFilter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CssUtils">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Cube">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CullingUtils">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\DateTime">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\DateTimeRange">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\DebugImageLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\DecalLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\DepthOffset">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Draggers">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\DrapeableNode">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\DrapingCullSet">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\DrapingTechnique">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\DrawInstanced">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\EarthManipulator">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ECEF">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Elevation">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ElevationLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ElevationLOD">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ElevationPool">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ElevationQuery">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ElevationRanges">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\EllipseNode">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Ellipsoid">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Endian">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Ephemeris">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ExampleResources">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Export">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Expression">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Extension">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ExtrudeGeometryFilter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ExtrusionSymbol">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FadeEffect">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Feature">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FeatureCursor">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FeatureDisplayLayout">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FeatureElevationLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FeatureImageLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FeatureIndex">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FeatureModelGraph">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FeatureModelLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FeatureModelSource">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FeatureNode">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FeatureRasterizer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FeatureSDFLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FeatureSource">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FeatureSourceIndexNode">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FeatureStyleSorter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FileUtils">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Fill">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Filter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FilterContext">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FilteredFeatureSource">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FlatteningLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Formatter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FractalElevationLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FrameClock">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GARSGraticule">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GeoCommon">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GeoData">
    </None>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GeographicLibAdapter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GeosAdapter.cpp" />
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GeodeticGraticule">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GeodeticLabelingEngine">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Geoid">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GeoMath">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Geometry">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GeometryClamper">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GeometryCloud">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GeometryCompiler">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GeometryFactory">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GeometryRasterizer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GeometryUtils">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GeosUtils">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GeoPositionNode">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GeoPositionNodeAutoScaler">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GEOS">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GeoTransform">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GLSLChunker">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GLUtils">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GraticuleLabelingEngine">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\HeightFieldUtils">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Horizon">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\HorizonClipPlane">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\HTM">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\HTTPClient">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\IconResource">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\IconSymbol">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ImageLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ImageMosaic">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ImageOverlay">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ImageOverlayEditor">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ImageToFeatureLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ImageToHeightFieldConverter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ImageUtils">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\InstanceBuilder">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\InstanceCloud">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\InstanceResource">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\InstanceSymbol">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\IntersectionPicker">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\IOTypes">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\JoinLines">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\JsonUtils">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LabelNode">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LandCover">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LandCoverLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LatLongFormatter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Layer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LayerReference">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LayerShader">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Lighting">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LinearLineOfSight">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LineDrawable">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LineFunctor">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LineOfSight">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LineSymbol">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LoadableNode">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LocalGeometryNode">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LocalTangentPlane">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Locators">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LODGenerator">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LogarithmicDepthBuffer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Map">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MapboxGLGlyphManager">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MapboxGLImageLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MapCallback">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MapModelChange">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MapNode">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MapNodeObserver">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MaterialLoader">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Math">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MBTiles">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MeasureTool">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MemCache">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MemoryUtils">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MeshConsolidator">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MeshFlattener">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MeshSubdivider">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MetadataNode">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MetaTile">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Metrics">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MGRSFormatter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MGRSGraticule">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ModelLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ModelNode">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ModelResource">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ModelSource">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ModelSymbol">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MVT">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\NativeProgramAdapter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\NetworkMonitor">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\NodeUtils">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\NoiseTextureFactory">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Notify">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ObjectIDPicker">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ObjectIndex">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\optional">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\OverlayDecorator">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\PagedNode">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\PatchLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\PBRMaterial">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\PhongLightingEffect">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Picker">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\PlaceNode">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\PluginLoader">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\PointDrawable">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\PointSymbol">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\PolygonizeLines">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\PolygonSymbol">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\PowerlineLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\PrimitiveIntersector">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Profile">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Progress">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ProjectionUtils">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Query">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\RadialLineOfSight">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Random">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\RectangleNode">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\RefinePolicy">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Registry">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\RenderSymbol">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ResampleFilter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Resource">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ResourceCache">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ResourceLibrary">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Revisioning">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\RTTPicker">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ScaleFilter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ScatterFilter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\SceneGraphCallback">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ScreenSpaceLayout">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ScreenSpaceLayoutCallout">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ScreenSpaceLayoutDeclutter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ScreenSpaceLayoutImpl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Script">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ScriptEngine">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ScriptFilter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\SDF">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\SelectExtentTool">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Session">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ShaderFactory">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ShaderGenerator">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ShaderLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ShaderLoader">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ShaderMerger">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Shaders">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ShaderUtils">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Shadowing">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\SimpleOceanLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\SimplePager">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\SimplexNoise">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\SimplifyFilter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\SimplifiedSpatialReference">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Skins">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Sky">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\SkyView">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\StarData">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\StateSetCache">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\StateTransition">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Status">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\StringUtils">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Stroke">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Style">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\StyleSelector">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\StyleSheet">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\SubstituteModelFilter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Symbol">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Tags">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TDTiles">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Terrain">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TerrainConstraintLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TerrainEffect">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TerrainEngineNode">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TerrainEngineRequirements">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TerrainLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TerrainMeshLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TerrainOptions">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TerrainProfile">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TerrainResources">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TerrainTileModel">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TerrainTileModelFactory">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TerrainTileNode">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TessellateOperator">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Tessellator">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Text">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TextSymbol">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TextSymbolizer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TextureArena">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TextureBuffer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TFS">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TFSPackager">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Threading">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ThreeDTilesLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TileCache">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TiledFeatureModelLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TiledModelLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TileEstimator">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TileHandler">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TileIndex">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TileIndexBuilder">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TileKey">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TileLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TileMesher">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TileRasterizer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TileSource">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TileSourceElevationLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TileSourceImageLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TileVisitor">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TimeControl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TimeSeriesImage">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TMS">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TMSBackFiller">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TopologyGraph">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TrackNode">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TransformFilter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Units">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\URI">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Utils">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\UTMGraticule">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\UTMLabelingEngine">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\VerticalDatum">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\VideoLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ViewFitter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Viewpoint">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\VirtualProgram">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\VisibleLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\WFS">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\WindLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\WireLines">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\WMS">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\XmlUtils">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\XYZ">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\XYZFeatureSource">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\XYZModelLayer">
    </None>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\rtree.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\weemesh.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\weejobs.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\tinyxml\tinyxml.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\tinyxml\tinystr.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarth\vector_tile.pb.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarth\glyphs.pb.h" />
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\build_include\osgEarth\Version">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\build_include\osgEarth\BuildConfig">
    </None>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\AltitudeFilter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\AltitudeSymbol.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\AnnotationData.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\AnnotationLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\AnnotationNode.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\AnnotationRegistry.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\AnnotationSettings.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\AnnotationUtils.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ArcGISServer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ArcGISTilePackage.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\AtlasBuilder.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\AttributesFilter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\AutoClipPlaneHandler.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\AzureMaps.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\BboxDrawable.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\BBoxSymbol.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\BillboardResource.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\BillboardSymbol.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Bing.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Bounds.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\BufferFilter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\BuildGeometryFilter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\BuildTextFilter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Cache.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CacheBin.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CachePolicy.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CacheSeed.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Callouts.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CameraUtils.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Capabilities.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CascadeDrapingDecorator.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CentroidFilter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CesiumIon.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Chonk.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CircleNode.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ClampableNode.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ClampCallback.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Clamping.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ClampingTechnique.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ClipSpace.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ClusterNode.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Color.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ColorFilter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Composite.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CompositeTiledModelLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Compressors.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CompressedArray.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Config.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ContourMap.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ConvertTypeFilter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CoverageLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CoverageSymbol.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CropFilter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CssUtils.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Cube.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CullingUtils.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\DateTime.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\DateTimeRange.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\DebugImageLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\DecalLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\DepthOffset.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Draggers.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\DrapeableNode.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\DrapingCullSet.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\DrapingTechnique.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\DrawInstanced.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\EarthManipulator.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ECEF.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Elevation.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ElevationLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ElevationLOD.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ElevationPool.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ElevationQuery.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ElevationRanges.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\EllipseNode.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Ellipsoid.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Ephemeris.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ExampleResources.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Expression.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Extension.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ExtrudeGeometryFilter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ExtrusionSymbol.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FadeEffect.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Feature.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FeatureCursor.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FeatureDisplayLayout.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FeatureElevationLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FeatureImageLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FeatureModelGraph.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FeatureModelLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FeatureModelSource.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FeatureNode.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FeatureRasterizer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FeatureSDFLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FeatureSource.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FeatureSourceIndexNode.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FeatureStyleSorter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FileGDBFeatureSource.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FileUtils.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Fill.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Filter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FilterContext.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FilteredFeatureSource.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FlatteningLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FractalElevationLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\FrameClock.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GARSGraticule.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GeoData.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GeodeticGraticule.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GeodeticLabelingEngine.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Geoid.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GeoMath.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Geometry.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GeometryClamper.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GeometryCloud.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GeometryCompiler.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GeometryFactory.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GeometryRasterizer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GeometryUtils.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GeoPositionNode.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GeoPositionNodeAutoScaler.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GEOS.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GeoTransform.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GLSLChunker.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GLUtils.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GraticuleLabelingEngine.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\HeightFieldUtils.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Horizon.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\HorizonClipPlane.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\HTM.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\HTTPClient.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\IconResource.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\IconSymbol.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ImageLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ImageMosaic.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ImageOverlay.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ImageOverlayEditor.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ImageToFeatureLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ImageToHeightFieldConverter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ImageUtils.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\InstanceBuilder.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\InstanceCloud.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\InstanceResource.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\InstanceSymbol.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\IntersectionPicker.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\IOTypes.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\JoinLines.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\JsonUtils.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LabelNode.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LandCover.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LandCoverLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LatLongFormatter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Layer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LayerShader.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Lighting.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LinearLineOfSight.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LineDrawable.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LineSymbol.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LocalGeometryNode.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LocalTangentPlane.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LODGenerator.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LogarithmicDepthBuffer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Map.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MapboxGLGlyphManager.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MapboxGLImageLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MapCallback.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MapNode.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MaterialLoader.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Math.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MBTiles.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MeasureTool.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MemCache.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MemoryUtils.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MeshConsolidator.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MeshFlattener.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MeshSubdivider.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MetadataNode.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MetaTile.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Metrics.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MGRSFormatter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MGRSGraticule.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ModelLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ModelNode.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ModelResource.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ModelSource.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ModelSymbol.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MVT.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\NetworkMonitor.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\NodeUtils.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\NoiseTextureFactory.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Notify.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ObjectIDPicker.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ObjectIndex.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\OverlayDecorator.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\PagedNode.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\PatchLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\PBRMaterial.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\PhongLightingEffect.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\PlaceNode.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\PointDrawable.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\PointSymbol.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\PolygonizeLines.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\PolygonSymbol.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\PowerlineLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\PrimitiveIntersector.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Profile.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Progress.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Query.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\RadialLineOfSight.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Random.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\RectangleNode.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Registry.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\RenderSymbol.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ResampleFilter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Resource.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ResourceCache.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ResourceLibrary.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Revisioning.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\RTTPicker.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ScaleFilter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ScatterFilter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\SceneGraphCallback.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ScreenSpaceLayout.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ScriptEngine.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ScriptFilter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\SDF.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\SelectExtentTool.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Session.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ShaderFactory.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ShaderGenerator.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ShaderLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ShaderLoader.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ShaderMerger.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ShaderUtils.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Shadowing.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\SimpleOceanLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\SimplePager.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\SimplexNoise.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\SimplifyFilter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Skins.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Sky.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\SkyView.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\StateSetCache.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Status.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\StringUtils.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Stroke.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Style.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\StyleSelector.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\StyleSheet.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\SubstituteModelFilter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Symbol.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TDTiles.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Terrain.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TerrainConstraintLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TerrainEngineNode.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TerrainMeshLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TerrainOptions.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TerrainProfile.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TerrainResources.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TerrainTileModel.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TerrainTileModelFactory.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TessellateOperator.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Tessellator.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Text.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TextSymbol.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TextSymbolizer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TextureArena.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TextureBuffer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TextureBufferSerializer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TFS.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TFSPackager.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Threading.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ThreeDTilesLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TileCache.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TiledFeatureModelLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TiledModelLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TileEstimator.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TileHandler.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TileIndex.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TileIndexBuilder.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TileKey.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TileLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TileMesher.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TileRasterizer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TileSource.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TileSourceElevationLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TileSourceImageLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TileVisitor.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TimeControl.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TimeSeriesImage.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TMS.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TMSBackFiller.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TopologyGraph.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TrackNode.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\TransformFilter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Units.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\URI.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Utils.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\UTMGraticule.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\UTMLabelingEngine.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Version.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\VerticalDatum.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\VideoLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ViewFitter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Viewpoint.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\VirtualProgram.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\VisibleLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\WFS.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\WindLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\WireLines.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\WMS.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\XmlUtils.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\XYZ.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\XYZFeatureSource.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\XYZModelLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\tinyxml\tinyxml.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\tinyxml\tinyxmlerror.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\tinyxml\tinystr.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\tinyxml\tinyxmlparser.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarth\AutoGenShaders.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarth\vector_tile.pb.cc" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\src\osgEarth\glyphs.pb.cc" />
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\CascadeDraping.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Chonk.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Chonk.Culling.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\DepthOffset.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Draping.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\DrawInstancedAttribute.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GPUClamping.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GPUClamping.lib.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\HexTiling.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Instancing.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LineDrawable.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\MetadataNode.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\WireLines.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\PhongLighting.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\PointDrawable.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Text.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Text_legacy.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ContourMap.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\GeodeticGraticule.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LogDepthBuffer.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\LogDepthBuffer.VertOnly.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\ShadowCaster.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\SimpleOceanLayer.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\RTTPicker.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\WindLayer.CS.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\PBR.glsl">
    </None>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\BuildConfig.in" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Version.in" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\src\osgEarth\Shaders.cpp.in" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-simp-qiu\build_desk\ZERO_CHECK.vcxproj">
      <Project>{F6C31C9C-B23A-34A9-9FDF-64F2E3F0BC91}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>