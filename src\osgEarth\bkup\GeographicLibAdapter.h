/**
 * rocky c++
 * Copyright 2023 Pelican Mapping
 * MIT License
 */
#pragma once

#include <osgEarth/Common>
#include <GeographicLib/GeoCoords.hpp>
#include <GeographicLib/UTMUPS.hpp>
#include <GeographicLib/TransverseMercator.hpp>
#include <GeographicLib/PolarStereographic.hpp>
#include <GeographicLib/Geodesic.hpp>
#include <GeographicLib/LocalCartesian.hpp>
#include <GeographicLib/Geocentric.hpp>
#include <GeographicLib/Constants.hpp>
#include <GeographicLib/Math.hpp>
#include <string>
#include <memory>

namespace osgEarth
{
    /**
     * GeographicLib适配器 - 替代PROJ库功能
     * 提供坐标系统转换和投影功能
     */
    class OSGEARTH_EXPORT GeographicLibAdapter
    {
    public:
        //! 投影类型枚举
        enum class ProjectionType
        {
            GEOGRAPHIC,     // 地理坐标 (经纬度)
            GEOCENTRIC,     // 地心坐标 (ECEF)
            UTM,            // UTM投影
            MERCATOR,       // 墨卡托投影
            POLAR_STEREO,   // 极地立体投影
            LOCAL_CARTESIAN // 本地笛卡尔坐标
        };

        //! 坐标转换操作句柄
        struct TransformHandle
        {
            ProjectionType fromType = ProjectionType::GEOGRAPHIC;
            ProjectionType toType = ProjectionType::GEOGRAPHIC;

            // UTM参数
            int utmZone = 0;
            bool utmNorth = true;

            // 墨卡托参数
            double centralMeridian = 0.0;
            double falseEasting = 0.0;
            double falseNorthing = 0.0;

            // 椭球体参数
            double semiMajorAxis = GeographicLib::Constants::WGS84_a();
            double flattening = GeographicLib::Constants::WGS84_f();

            // 本地坐标原点
            double localOriginLat = 0.0;
            double localOriginLon = 0.0;
            double localOriginHeight = 0.0;

            // 错误信息
            mutable std::string lastError;

            // GeographicLib对象
            mutable std::unique_ptr<GeographicLib::LocalCartesian> localCartesian;
            mutable std::unique_ptr<GeographicLib::TransverseMercator> transverseMercator;
            mutable std::unique_ptr<GeographicLib::PolarStereographic> polarStereographic;
        };

    public:
        //! 创建坐标转换句柄
        //! @param fromDef 源坐标系定义字符串
        //! @param toDef 目标坐标系定义字符串
        //! @return 转换句柄，失败时返回nullptr
        static std::shared_ptr<TransformHandle> createTransform(
            const std::string &fromDef,
            const std::string &toDef);

        //! 正向坐标转换
        //! @param handle 转换句柄
        //! @param x X坐标（输入输出）
        //! @param y Y坐标（输入输出）
        //! @param z Z坐标（输入输出）
        //! @return 转换是否成功
        static bool forward(
            std::shared_ptr<TransformHandle> handle,
            double &x, double &y, double &z);

        //! 反向坐标转换
        //! @param handle 转换句柄
        //! @param x X坐标（输入输出）
        //! @param y Y坐标（输入输出）
        //! @param z Z坐标（输入输出）
        //! @return 转换是否成功
        static bool inverse(
            std::shared_ptr<TransformHandle> handle,
            double &x, double &y, double &z);

        //! 批量正向坐标转换
        //! @param handle 转换句柄
        //! @param x X坐标数组
        //! @param y Y坐标数组
        //! @param z Z坐标数组
        //! @param stride 数组步长
        //! @param count 点数量
        //! @return 转换是否成功
        static bool forwardArray(
            std::shared_ptr<TransformHandle> handle,
            double *x, double *y, double *z,
            std::size_t stride, std::size_t count);

        //! 批量反向坐标转换
        //! @param handle 转换句柄
        //! @param x X坐标数组
        //! @param y Y坐标数组
        //! @param z Z坐标数组
        //! @param stride 数组步长
        //! @param count 点数量
        //! @return 转换是否成功
        static bool inverseArray(
            std::shared_ptr<TransformHandle> handle,
            double *x, double *y, double *z,
            std::size_t stride, std::size_t count);

        //! 解析坐标系定义字符串
        //! @param definition 坐标系定义
        //! @param handle 输出的转换句柄参数
        //! @return 解析是否成功
        static bool parseDefinition(const std::string &definition, TransformHandle &handle);

        //! 获取GeographicLib版本
        //! @return 版本字符串
        static std::string version();

        //! 获取错误信息
        //! @param handle 转换句柄
        //! @return 错误信息字符串
        static std::string getError(std::shared_ptr<TransformHandle> handle);

    private:
        //! 执行地理坐标到投影坐标的转换
        static bool geoToProj(const TransformHandle &handle, double &x, double &y, double &z);

        //! 执行投影坐标到地理坐标的转换
        static bool projToGeo(const TransformHandle &handle, double &x, double &y, double &z);

        //! 执行地理坐标到地心坐标的转换
        static bool geoToGeocentric(double &x, double &y, double &z);

        //! 执行地心坐标到地理坐标的转换
        static bool geocentricToGeo(double &x, double &y, double &z);
    };
}
