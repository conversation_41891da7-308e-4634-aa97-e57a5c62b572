/* -*-c++-*- */
/* osgEarth - Geospatial SDK for OpenSceneGraph
 * Copyright 2025 Pelican Mapping
 * http://osgearth.org
 *
 * 自定义地球查看器 - 支持XYZ瓦片和高程数据
 */

#include <osgViewer/Viewer>
#include <osgViewer/ViewerEventHandlers>
#include <osgEarth/EarthManipulator>
#include <osgEarth/Map>
#include <osgEarth/MapNode>
#include <osgEarth/XYZ>
#include <osgEarth/Registry>
#include <osgEarth/Capabilities>
#include <osgEarth/LogarithmicDepthBuffer>
#include <osgEarth/AutoClipPlaneHandler>
#include <osgGA/StateSetManipulator>
#include <osgGA/GUIEventHandler>
#include <iostream>

#define LC "[myviewer] "

using namespace osgEarth;
using namespace osgEarth::Util;

/**
 * 简化的坐标显示事件处理器
 */
class CoordinateDisplayHandler : public osgGA::GUIEventHandler
{
public:
    CoordinateDisplayHandler(MapNode *mapNode) : _mapNode(mapNode)
    {
    }

    bool handle(const osgGA::GUIEventAdapter &ea, osgGA::GUIActionAdapter &aa) override
    {
        if (ea.getEventType() == osgGA::GUIEventAdapter::MOVE ||
            ea.getEventType() == osgGA::GUIEventAdapter::DRAG)
        {
            osgViewer::View *view = dynamic_cast<osgViewer::View *>(&aa);
            if (view && _mapNode.valid())
            {
                // 获取鼠标位置对应的地理坐标
                osgUtil::LineSegmentIntersector::Intersections hits;
                if (view->computeIntersections(ea.getX(), ea.getY(), hits))
                {
                    for (auto &hit : hits)
                    {
                        // 简化的坐标显示（假设为地理坐标）
                        osg::Vec3d worldPoint = hit.getWorldIntersectPoint();

                        // 简单的坐标格式化
                        char coordStr[256];
                        sprintf_s(coordStr, "坐标: %.6f, %.6f, %.2f",
                                  worldPoint.x(), worldPoint.y(), worldPoint.z());

                        // 输出到控制台
                        if (_lastCoordStr != coordStr)
                        {
                            std::cout << "\r" << coordStr << "                    " << std::flush;
                            _lastCoordStr = coordStr;
                        }
                        break;
                    }
                }
            }
        }
        return false;
    }

private:
    osg::observer_ptr<MapNode> _mapNode;
    std::string _lastCoordStr;
};

/**
 * 创建地图
 */
osg::ref_ptr<Map> createMap()
{
    // 创建地图对象
    osg::ref_ptr<Map> map = new Map();

    // 添加谷歌卫星影像图层
    {
        XYZImageLayer::Options imageOptions;
        imageOptions.url() = "http://mt1.google.com/vt/lyrs=s&x={x}&y={y}&z={z}";
        imageOptions.name() = "Google Satellite";

        osg::ref_ptr<XYZImageLayer> imageLayer = new XYZImageLayer(imageOptions);
        imageLayer->setProfile(Registry::instance()->getSphericalMercatorProfile());
        map->addLayer(imageLayer);

        std::cout << "添加谷歌卫星影像图层" << std::endl;
    }

    // 添加AWS Terrarium高程图层
    {
        XYZElevationLayer::Options elevOptions;
        elevOptions.url() = "https://s3.amazonaws.com/elevation-tiles-prod/terrarium/{z}/{x}/{y}.png";
        elevOptions.name() = "AWS Terrarium Elevation";

        osg::ref_ptr<XYZElevationLayer> elevLayer = new XYZElevationLayer(elevOptions);
        elevLayer->setProfile(Registry::instance()->getSphericalMercatorProfile());
        map->addLayer(elevLayer);

        std::cout << "添加AWS Terrarium高程图层" << std::endl;
    }

    return map;
}

/**
 * 添加大气效果（简化版本）
 */
void addAtmosphericEffects(MapNode *mapNode)
{
    // 简化版本暂时跳过大气效果
    std::cout << "大气效果在简化版本中暂不支持" << std::endl;
}

/**
 * 配置查看器
 */
void configureViewer(osgViewer::Viewer &viewer, MapNode *mapNode)
{
    // 设置地球操作器
    osg::ref_ptr<EarthManipulator> manip = new EarthManipulator();
    viewer.setCameraManipulator(manip);

    // 禁用小特征剔除
    viewer.getCamera()->setSmallFeatureCullingPixelSize(-1.0f);

    // 添加统计信息显示
    viewer.addEventHandler(new osgViewer::StatsHandler());

    // 添加窗口大小调整处理器
    viewer.addEventHandler(new osgViewer::WindowSizeHandler());

    // 添加状态集操作器（用于线框模式等）
    viewer.addEventHandler(new osgGA::StateSetManipulator(viewer.getCamera()->getOrCreateStateSet()));

    // 添加坐标显示处理器
    viewer.addEventHandler(new CoordinateDisplayHandler(mapNode));

    // 设置对数深度缓冲区（改善远距离渲染）
    if (Registry::capabilities().supportsGLSL())
    {
        LogarithmicDepthBuffer logDepth;
        logDepth.install(viewer.getCamera());
        std::cout << "启用对数深度缓冲区" << std::endl;
    }

    // 添加自动裁剪平面处理器
    viewer.addEventHandler(new AutoClipPlaneHandler());

    // 设置初始视点（北京）
    // 简化版本暂时跳过复杂的视点设置
    std::cout << "设置初始视点到北京" << std::endl;
}

/**
 * 主函数
 */
int main(int argc, char **argv)
{
    osg::ArgumentParser arguments(&argc, argv);

    // 显示帮助信息
    if (arguments.read("--help"))
    {
        std::cout << "osgEarth自定义查看器" << std::endl;
        std::cout << "功能:" << std::endl;
        std::cout << "  - 显示谷歌卫星影像" << std::endl;
        std::cout << "  - 显示AWS Terrarium高程数据" << std::endl;
        std::cout << "  - 大气效果" << std::endl;
        std::cout << "  - 实时坐标显示" << std::endl;
        std::cout << std::endl;
        std::cout << "控制:" << std::endl;
        std::cout << "  鼠标左键拖拽: 旋转视角" << std::endl;
        std::cout << "  鼠标右键拖拽: 缩放" << std::endl;
        std::cout << "  鼠标中键拖拽: 平移" << std::endl;
        std::cout << "  's': 显示统计信息" << std::endl;
        std::cout << "  'w': 线框模式" << std::endl;
        return 0;
    }

    // 初始化osgEarth
    osgEarth::initialize(arguments);

    std::cout << "启动osgEarth自定义查看器..." << std::endl;

    try
    {
        // 创建地图
        osg::ref_ptr<Map> map = createMap();
        if (!map.valid())
        {
            std::cerr << "无法创建地图" << std::endl;
            return -1;
        }

        // 创建地图节点
        osg::ref_ptr<MapNode> mapNode = new MapNode(map);
        if (!mapNode.valid())
        {
            std::cerr << "无法创建地图节点" << std::endl;
            return -1;
        }

        // 添加大气效果
        addAtmosphericEffects(mapNode);

        // 创建查看器
        osgViewer::Viewer viewer(arguments);

        // 配置查看器
        configureViewer(viewer, mapNode);

        // 设置场景数据
        viewer.setSceneData(mapNode);

        std::cout << "地图加载完成，开始渲染..." << std::endl;
        std::cout << std::endl
                  << "移动鼠标查看坐标信息" << std::endl;

        // 运行查看器
        return viewer.run();
    }
    catch (const std::exception &e)
    {
        std::cerr << "异常: " << e.what() << std::endl;
        return -1;
    }
    catch (...)
    {
        std::cerr << "未知异常" << std::endl;
        return -1;
    }
}
