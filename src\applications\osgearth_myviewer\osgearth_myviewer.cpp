/* -*-c++-*- */
/* osgEarth - Geospatial SDK for OpenSceneGraph
 * Copyright 2025 Pelican Mapping
 * http://osgearth.org
 *
 * 简化的地球查看器 - 基本OSG功能演示
 */

#include <osgViewer/Viewer>
#include <osgViewer/ViewerEventHandlers>
#include <osg/ShapeDrawable>
#include <osg/Geode>
#include <osg/Group>
#include <osg/MatrixTransform>
#include <osg/Texture2D>
#include <osg/Material>
#include <osgGA/TrackballManipulator>
#include <osgGA/StateSetManipulator>
#include <osgGA/GUIEventHandler>
#include <osgDB/ReadFile>
#include <iostream>
#include <osg/ArgumentParser>

/**
 * 创建一个简单的地球模型
 */
osg::ref_ptr<osg::Node> createEarth()
{
    // 创建一个球体作为地球
    osg::ref_ptr<osg::Sphere> sphere = new osg::Sphere(osg::Vec3(0, 0, 0), 6378137.0f); // 地球半径（米）
    osg::ref_ptr<osg::ShapeDrawable> sphereDrawable = new osg::ShapeDrawable(sphere);

    // 设置地球材质
    osg::ref_ptr<osg::Material> material = new osg::Material;
    material->setDiffuse(osg::Material::FRONT_AND_BACK, osg::Vec4(0.2f, 0.5f, 1.0f, 1.0f)); // 蓝色
    material->setSpecular(osg::Material::FRONT_AND_BACK, osg::Vec4(0.8f, 0.8f, 0.8f, 1.0f));
    material->setShininess(osg::Material::FRONT_AND_BACK, 64.0f);

    osg::ref_ptr<osg::Geode> geode = new osg::Geode;
    geode->addDrawable(sphereDrawable);
    geode->getOrCreateStateSet()->setAttributeAndModes(material, osg::StateAttribute::ON);

    // 尝试加载地球纹理
    osg::ref_ptr<osg::Texture2D> texture = new osg::Texture2D;
    osg::ref_ptr<osg::Image> image = osgDB::readImageFile("earth.jpg");
    if (image.valid())
    {
        texture->setImage(image);
        geode->getOrCreateStateSet()->setTextureAttributeAndModes(0, texture, osg::StateAttribute::ON);
        std::cout << "Earth texture loaded" << std::endl;
    }
    else
    {
        std::cout << "Earth texture file earth.jpg not found, using default material" << std::endl;
    }

    return geode;
}

/**
 * 创建坐标系网格
 */
osg::ref_ptr<osg::Node> createCoordinateGrid()
{
    osg::ref_ptr<osg::Group> group = new osg::Group;

    // 创建经纬度网格线
    osg::ref_ptr<osg::Geometry> geometry = new osg::Geometry;
    osg::ref_ptr<osg::Vec3Array> vertices = new osg::Vec3Array;

    const float radius = 6378137.0f * 1.01f; // 稍微大于地球半径
    const int numLines = 36;                 // 每10度一条线

    // 经线
    for (int i = 0; i < numLines; ++i)
    {
        float lon = (float)i * 360.0f / numLines;
        float lonRad = osg::DegreesToRadians(lon);

        for (int j = 0; j <= 180; ++j)
        {
            float lat = (float)j * 180.0f / 180.0f - 90.0f;
            float latRad = osg::DegreesToRadians(lat);

            float x = radius * cos(latRad) * cos(lonRad);
            float y = radius * cos(latRad) * sin(lonRad);
            float z = radius * sin(latRad);

            vertices->push_back(osg::Vec3(x, y, z));
        }
    }

    // 纬线
    for (int i = 0; i < 18; ++i) // 每10度一条线
    {
        float lat = (float)i * 180.0f / 18.0f - 90.0f;
        float latRad = osg::DegreesToRadians(lat);

        for (int j = 0; j <= 360; ++j)
        {
            float lon = (float)j * 360.0f / 360.0f;
            float lonRad = osg::DegreesToRadians(lon);

            float x = radius * cos(latRad) * cos(lonRad);
            float y = radius * cos(latRad) * sin(lonRad);
            float z = radius * sin(latRad);

            vertices->push_back(osg::Vec3(x, y, z));
        }
    }

    geometry->setVertexArray(vertices);

    // 设置颜色
    osg::ref_ptr<osg::Vec4Array> colors = new osg::Vec4Array;
    colors->push_back(osg::Vec4(1.0f, 1.0f, 0.0f, 0.5f)); // 黄色半透明
    geometry->setColorArray(colors);
    geometry->setColorBinding(osg::Geometry::BIND_OVERALL);

    // 设置绘制模式
    geometry->addPrimitiveSet(new osg::DrawArrays(osg::PrimitiveSet::POINTS, 0, vertices->size()));

    osg::ref_ptr<osg::Geode> geode = new osg::Geode;
    geode->addDrawable(geometry);

    // 设置点大小
    osg::ref_ptr<osg::Point> pointAttr = new osg::Point;
    pointAttr->setSize(2.0f);
    geode->getOrCreateStateSet()->setAttributeAndModes(pointAttr, osg::StateAttribute::ON);

    group->addChild(geode);
    return group;
}

/**
 * 简化的坐标显示事件处理器
 */
class CoordinateDisplayHandler : public osgGA::GUIEventHandler
{
public:
    CoordinateDisplayHandler() {}

    bool handle(const osgGA::GUIEventAdapter &ea, osgGA::GUIActionAdapter &aa) override
    {
        if (ea.getEventType() == osgGA::GUIEventAdapter::MOVE ||
            ea.getEventType() == osgGA::GUIEventAdapter::DRAG)
        {
            osgViewer::View *view = dynamic_cast<osgViewer::View *>(&aa);
            if (view)
            {
                // 获取鼠标位置对应的3D坐标
                osgUtil::LineSegmentIntersector::Intersections hits;
                if (view->computeIntersections(ea.getX(), ea.getY(), hits))
                {
                    for (auto &hit : hits)
                    {
                        // 显示3D世界坐标
                        osg::Vec3d worldPoint = hit.getWorldIntersectPoint();

                        // 转换为地理坐标（简化计算）
                        double radius = worldPoint.length();
                        double lat = asin(worldPoint.z() / radius) * 180.0 / osg::PI;
                        double lon = atan2(worldPoint.y(), worldPoint.x()) * 180.0 / osg::PI;

                        // 坐标格式化
                        char coordBuffer[256];
                        sprintf_s(coordBuffer, "Longitude: %.6f, Latitude: %.6f, Height: %.0fm",
                                  lon, lat, radius - 6378137.0);

                        // 输出到控制台
                        std::string coordStr(coordBuffer);
                        if (_lastCoordStr != coordStr)
                        {
                            std::cout << "\r" << coordStr << "                    " << std::flush;
                            _lastCoordStr = coordStr;
                        }
                        break;
                    }
                }
            }
        }
        return false;
    }

private:
    std::string _lastCoordStr;
};

/**
 * 配置查看器
 */
void configureViewer(osgViewer::Viewer &viewer)
{
    // 设置轨迹球操作器
    osg::ref_ptr<osgGA::TrackballManipulator> manip = new osgGA::TrackballManipulator();
    viewer.setCameraManipulator(manip);

    // 禁用小特征剔除
    viewer.getCamera()->setSmallFeatureCullingPixelSize(-1.0f);

    // 添加统计信息显示
    viewer.addEventHandler(new osgViewer::StatsHandler());

    // 添加窗口大小调整处理器
    viewer.addEventHandler(new osgViewer::WindowSizeHandler());

    // 添加状态集操作器（用于线框模式等）
    viewer.addEventHandler(new osgGA::StateSetManipulator(viewer.getCamera()->getOrCreateStateSet()));

    // 添加坐标显示处理器
    viewer.addEventHandler(new CoordinateDisplayHandler());

    // 设置初始视点
    manip->setDistance(6378137.0 * 3.0); // 距离地心3倍地球半径

    std::cout << "查看器配置完成" << std::endl;
}

/**
 * 主函数
 */
int main(int argc, char **argv)
{
    osg::ArgumentParser arguments(&argc, argv);

    // 显示帮助信息
    if (arguments.read("--help"))
    {
        std::cout << "简化地球查看器" << std::endl;
        std::cout << "功能:" << std::endl;
        std::cout << "  - 显示3D地球模型" << std::endl;
        std::cout << "  - 坐标网格显示" << std::endl;
        std::cout << "  - 实时坐标显示" << std::endl;
        std::cout << std::endl;
        std::cout << "控制:" << std::endl;
        std::cout << "  鼠标左键拖拽: 旋转视角" << std::endl;
        std::cout << "  鼠标右键拖拽: 缩放" << std::endl;
        std::cout << "  鼠标中键拖拽: 平移" << std::endl;
        std::cout << "  's': 显示统计信息" << std::endl;
        std::cout << "  'w': 线框模式" << std::endl;
        return 0;
    }

    std::cout << "启动简化地球查看器..." << std::endl;

    try
    {
        // 创建场景根节点
        osg::ref_ptr<osg::Group> root = new osg::Group;

        // 创建地球模型
        osg::ref_ptr<osg::Node> earth = createEarth();
        root->addChild(earth);

        // 创建坐标网格
        osg::ref_ptr<osg::Node> grid = createCoordinateGrid();
        root->addChild(grid);

        // 创建查看器
        osgViewer::Viewer viewer(arguments);

        // 配置查看器
        configureViewer(viewer);

        // 设置场景数据
        viewer.setSceneData(root);

        std::cout << "Earth model loaded, starting rendering..." << std::endl;
        std::cout << std::endl
                  << "Move mouse to view coordinate information" << std::endl;
        std::cout << "Tip: If earth.jpg texture file exists, earth texture will be displayed" << std::endl;

        // 运行查看器
        return viewer.run();
    }
    catch (const std::exception &e)
    {
        std::cerr << "异常: " << e.what() << std::endl;
        return -1;
    }
    catch (...)
    {
        std::cerr << "未知异常" << std::endl;
        return -1;
    }
}
