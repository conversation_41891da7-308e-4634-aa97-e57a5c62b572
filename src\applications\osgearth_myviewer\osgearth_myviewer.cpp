/* -*-c++-*- */
/* osgEarth - Geospatial SDK for OpenSceneGraph
 * Copyright 2025 Pelican Mapping
 * http://osgearth.org
 *
 * Simplified Earth Viewer - Basic OSG functionality demo
 */

#include <osgViewer/Viewer>
#include <osgViewer/ViewerEventHandlers>
#include <osg/ShapeDrawable>
#include <osg/Geode>
#include <osg/Group>
#include <osg/MatrixTransform>
#include <osg/Texture2D>
#include <osg/Material>
#include <osg/Point>
#include <osgGA/TrackballManipulator>
#include <osgGA/StateSetManipulator>
#include <osgGA/GUIEventHandler>
#include <osgDB/ReadFile>
#include <iostream>
#include <osg/ArgumentParser>

/**
 * Create a simple Earth model
 */
osg::ref_ptr<osg::Node> createEarth()
{
    // Create a sphere as Earth
    osg::ref_ptr<osg::Sphere> sphere = new osg::Sphere(osg::Vec3(0, 0, 0), 6378137.0f); // Earth radius in meters
    osg::ref_ptr<osg::ShapeDrawable> sphereDrawable = new osg::ShapeDrawable(sphere);
    
    // Set Earth material
    osg::ref_ptr<osg::Material> material = new osg::Material;
    material->setDiffuse(osg::Material::FRONT_AND_BACK, osg::Vec4(0.2f, 0.5f, 1.0f, 1.0f)); // Blue
    material->setSpecular(osg::Material::FRONT_AND_BACK, osg::Vec4(0.8f, 0.8f, 0.8f, 1.0f));
    material->setShininess(osg::Material::FRONT_AND_BACK, 64.0f);
    
    osg::ref_ptr<osg::Geode> geode = new osg::Geode;
    geode->addDrawable(sphereDrawable);
    geode->getOrCreateStateSet()->setAttributeAndModes(material, osg::StateAttribute::ON);
    
    // Try to load Earth texture
    osg::ref_ptr<osg::Texture2D> texture = new osg::Texture2D;
    osg::ref_ptr<osg::Image> image = osgDB::readImageFile("earth.jpg");
    if (image.valid())
    {
        texture->setImage(image);
        geode->getOrCreateStateSet()->setTextureAttributeAndModes(0, texture, osg::StateAttribute::ON);
        std::cout << "Earth texture loaded" << std::endl;
    }
    else
    {
        std::cout << "Earth texture file earth.jpg not found, using default material" << std::endl;
    }
    
    return geode;
}

/**
 * Create coordinate grid
 */
osg::ref_ptr<osg::Node> createCoordinateGrid()
{
    osg::ref_ptr<osg::Group> group = new osg::Group;
    
    // Create latitude/longitude grid lines
    osg::ref_ptr<osg::Geometry> geometry = new osg::Geometry;
    osg::ref_ptr<osg::Vec3Array> vertices = new osg::Vec3Array;
    
    const float radius = 6378137.0f * 1.01f; // Slightly larger than Earth radius
    const int numLines = 36; // One line every 10 degrees
    
    // Meridians
    for (int i = 0; i < numLines; ++i)
    {
        float lon = (float)i * 360.0f / numLines;
        float lonRad = osg::DegreesToRadians(lon);
        
        for (int j = 0; j <= 180; ++j)
        {
            float lat = (float)j * 180.0f / 180.0f - 90.0f;
            float latRad = osg::DegreesToRadians(lat);
            
            float x = radius * cos(latRad) * cos(lonRad);
            float y = radius * cos(latRad) * sin(lonRad);
            float z = radius * sin(latRad);
            
            vertices->push_back(osg::Vec3(x, y, z));
        }
    }
    
    // Parallels
    for (int i = 0; i < 18; ++i) // One line every 10 degrees
    {
        float lat = (float)i * 180.0f / 18.0f - 90.0f;
        float latRad = osg::DegreesToRadians(lat);
        
        for (int j = 0; j <= 360; ++j)
        {
            float lon = (float)j * 360.0f / 360.0f;
            float lonRad = osg::DegreesToRadians(lon);
            
            float x = radius * cos(latRad) * cos(lonRad);
            float y = radius * cos(latRad) * sin(lonRad);
            float z = radius * sin(latRad);
            
            vertices->push_back(osg::Vec3(x, y, z));
        }
    }
    
    geometry->setVertexArray(vertices);
    
    // Set color
    osg::ref_ptr<osg::Vec4Array> colors = new osg::Vec4Array;
    colors->push_back(osg::Vec4(1.0f, 1.0f, 0.0f, 0.5f)); // Yellow semi-transparent
    geometry->setColorArray(colors);
    geometry->setColorBinding(osg::Geometry::BIND_OVERALL);
    
    // Set drawing mode
    geometry->addPrimitiveSet(new osg::DrawArrays(osg::PrimitiveSet::POINTS, 0, vertices->size()));
    
    osg::ref_ptr<osg::Geode> geode = new osg::Geode;
    geode->addDrawable(geometry);
    
    // Set point size
    osg::ref_ptr<osg::Point> pointAttr = new osg::Point;
    pointAttr->setSize(2.0f);
    geode->getOrCreateStateSet()->setAttributeAndModes(pointAttr, osg::StateAttribute::ON);
    
    group->addChild(geode);
    return group;
}

/**
 * Simplified coordinate display event handler
 */
class CoordinateDisplayHandler : public osgGA::GUIEventHandler
{
public:
    CoordinateDisplayHandler() {}

    bool handle(const osgGA::GUIEventAdapter& ea, osgGA::GUIActionAdapter& aa) override
    {
        if (ea.getEventType() == osgGA::GUIEventAdapter::MOVE ||
            ea.getEventType() == osgGA::GUIEventAdapter::DRAG)
        {
            osgViewer::View* view = dynamic_cast<osgViewer::View*>(&aa);
            if (view)
            {
                // Get 3D coordinates corresponding to mouse position
                osgUtil::LineSegmentIntersector::Intersections hits;
                if (view->computeIntersections(ea.getX(), ea.getY(), hits))
                {
                    for (auto& hit : hits)
                    {
                        // Display 3D world coordinates
                        osg::Vec3d worldPoint = hit.getWorldIntersectPoint();
                        
                        // Convert to geographic coordinates (simplified calculation)
                        double radius = worldPoint.length();
                        double lat = asin(worldPoint.z() / radius) * 180.0 / osg::PI;
                        double lon = atan2(worldPoint.y(), worldPoint.x()) * 180.0 / osg::PI;
                        
                        // Coordinate formatting
                        char coordBuffer[256];
                        sprintf_s(coordBuffer, "Longitude: %.6f, Latitude: %.6f, Height: %.0fm",
                                  lon, lat, radius - 6378137.0);

                        // Output to console
                        std::string coordStr(coordBuffer);
                        if (_lastCoordStr != coordStr)
                        {
                            std::cout << "\r" << coordStr << "                    " << std::flush;
                            _lastCoordStr = coordStr;
                        }
                        break;
                    }
                }
            }
        }
        return false;
    }

private:
    std::string _lastCoordStr;
};

/**
 * Configure viewer
 */
void configureViewer(osgViewer::Viewer& viewer)
{
    // Set trackball manipulator
    osg::ref_ptr<osgGA::TrackballManipulator> manip = new osgGA::TrackballManipulator();
    viewer.setCameraManipulator(manip);

    // Disable small feature culling
    viewer.getCamera()->setSmallFeatureCullingPixelSize(-1.0f);

    // Add statistics display
    viewer.addEventHandler(new osgViewer::StatsHandler());

    // Add window size adjustment handler
    viewer.addEventHandler(new osgViewer::WindowSizeHandler());

    // Add state set manipulator (for wireframe mode, etc.)
    viewer.addEventHandler(new osgGA::StateSetManipulator(viewer.getCamera()->getOrCreateStateSet()));

    // Add coordinate display handler
    viewer.addEventHandler(new CoordinateDisplayHandler());

    // Set initial viewpoint
    manip->setDistance(6378137.0 * 3.0); // Distance from Earth center: 3 times Earth radius
    
    std::cout << "Viewer configuration completed" << std::endl;
}

/**
 * Main function
 */
int main(int argc, char** argv)
{
    osg::ArgumentParser arguments(&argc, argv);

    // Display help information
    if (arguments.read("--help"))
    {
        std::cout << "Simplified Earth Viewer" << std::endl;
        std::cout << "Features:" << std::endl;
        std::cout << "  - Display 3D Earth model" << std::endl;
        std::cout << "  - Coordinate grid display" << std::endl;
        std::cout << "  - Real-time coordinate display" << std::endl;
        std::cout << std::endl;
        std::cout << "Controls:" << std::endl;
        std::cout << "  Left mouse drag: Rotate view" << std::endl;
        std::cout << "  Right mouse drag: Zoom" << std::endl;
        std::cout << "  Middle mouse drag: Pan" << std::endl;
        std::cout << "  's': Show statistics" << std::endl;
        std::cout << "  'w': Wireframe mode" << std::endl;
        return 0;
    }

    std::cout << "Starting simplified earth viewer..." << std::endl;

    try
    {
        // Create scene root node
        osg::ref_ptr<osg::Group> root = new osg::Group;

        // Create Earth model
        osg::ref_ptr<osg::Node> earth = createEarth();
        root->addChild(earth);

        // Create coordinate grid
        osg::ref_ptr<osg::Node> grid = createCoordinateGrid();
        root->addChild(grid);

        // Create viewer
        osgViewer::Viewer viewer(arguments);

        // Configure viewer
        configureViewer(viewer);

        // Set scene data
        viewer.setSceneData(root);

        std::cout << "Earth model loaded, starting rendering..." << std::endl;
        std::cout << std::endl << "Move mouse to view coordinate information" << std::endl;
        std::cout << "Tip: If earth.jpg texture file exists, earth texture will be displayed" << std::endl;

        // Run viewer
        return viewer.run();
    }
    catch (const std::exception& e)
    {
        std::cerr << "Exception: " << e.what() << std::endl;
        return -1;
    }
    catch (...)
    {
        std::cerr << "Unknown exception" << std::endl;
        return -1;
    }
}
