/* -*-c++-*- */
/* osgEarth - Geospatial SDK for OpenSceneGraph
 * Copyright 2025 Pelican Mapping
 * http://osgearth.org
 *
 * Custom Earth Viewer - Support XYZ tile layers and coordinate display
 */

#include <osgViewer/Viewer>
#include <osgViewer/ViewerEventHandlers>
#include <osgEarth/EarthManipulator>
#include <osgEarth/Map>
#include <osgEarth/MapNode>
#include <osgEarth/XYZ>
#include <osgEarth/Registry>
#include <osgEarth/Capabilities>
#include <osgEarth/LogarithmicDepthBuffer>
#include <osgEarth/AutoClipPlaneHandler>
#include <osgEarth/HTTPClient>
#include <osgEarth/GeodeticGraticule>
#include <osgEarth/ExampleResources>
#include <osgGA/StateSetManipulator>
#include <osgGA/GUIEventHandler>
#include <iostream>

#define LC "[myviewer] "

using namespace osgEarth;
using namespace osgEarth::Util;

/**
 * Configure network proxy
 */
void configureNetworkProxy()
{
    // Set HTTP proxy
    HTTPClient::setProxyHost("127.0.0.1");
    HTTPClient::setProxyPort(10809);
    
    // Set timeout
    HTTPClient::setDefaultTimeout(30);
    
    // Set concurrent connections
    HTTPClient::setMaxConcurrentRequests(8);
    
    std::cout << "Network proxy configured: 127.0.0.1:10809" << std::endl;
}

/**
 * Coordinate display event handler
 */
class CoordinateDisplayHandler : public osgGA::GUIEventHandler
{
public:
    CoordinateDisplayHandler(MapNode* mapNode) : _mapNode(mapNode)
    {
    }

    bool handle(const osgGA::GUIEventAdapter& ea, osgGA::GUIActionAdapter& aa) override
    {
        if (ea.getEventType() == osgGA::GUIEventAdapter::MOVE ||
            ea.getEventType() == osgGA::GUIEventAdapter::DRAG)
        {
            osgViewer::View* view = dynamic_cast<osgViewer::View*>(&aa);
            if (view && _mapNode.valid())
            {
                // Get geographic coordinates corresponding to mouse position
                osgUtil::LineSegmentIntersector::Intersections hits;
                if (view->computeIntersections(ea.getX(), ea.getY(), hits))
                {
                    for (auto& hit : hits)
                    {
                        // Get world coordinates
                        osg::Vec3d worldPoint = hit.getWorldIntersectPoint();
                        
                        // Convert to geographic coordinates
                        GeoPoint mapPoint;
                        mapPoint.fromWorld(_mapNode->getMapSRS(), worldPoint);
                        
                        // Format coordinates
                        char coordStr[256];
                        sprintf_s(coordStr, "Longitude: %.6f°, Latitude: %.6f°, Height: %.2fm", 
                                mapPoint.x(), mapPoint.y(), mapPoint.z());
                        
                        // Output to console
                        if (_lastCoordStr != coordStr)
                        {
                            std::cout << "\r" << coordStr << "                    " << std::flush;
                            _lastCoordStr = coordStr;
                        }
                        break;
                    }
                }
            }
        }
        return false;
    }

private:
    osg::observer_ptr<MapNode> _mapNode;
    std::string _lastCoordStr;
};

/**
 * Create map with XYZ layers
 */
osg::ref_ptr<Map> createMap()
{
    // Create map object
    osg::ref_ptr<Map> map = new Map();

    // Add Google satellite imagery layer
    {
        XYZImageLayer::Options imageOptions;
        imageOptions.url() = "http://mt1.google.com/vt/lyrs=s&x={x}&y={y}&z={z}";
        imageOptions.name() = "Google Satellite";
        imageOptions.profile() = Registry::instance()->getSphericalMercatorProfile();

        osg::ref_ptr<XYZImageLayer> imageLayer = new XYZImageLayer(imageOptions);
        map->addLayer(imageLayer);

        std::cout << "Added Google satellite imagery layer" << std::endl;
    }

    // Add OpenStreetMap layer (disabled by default)
    {
        XYZImageLayer::Options osmOptions;
        osmOptions.url() = "http://tile.openstreetmap.org/{z}/{x}/{y}.png";
        osmOptions.name() = "OpenStreetMap";
        osmOptions.enabled() = false;
        osmOptions.profile() = Registry::instance()->getSphericalMercatorProfile();

        osg::ref_ptr<XYZImageLayer> osmLayer = new XYZImageLayer(osmOptions);
        map->addLayer(osmLayer);

        std::cout << "Added OpenStreetMap layer (disabled)" << std::endl;
    }

    // Add coordinate graticule
    {
        GeodeticGraticule::Options graticuleOptions;
        graticuleOptions.name() = "Coordinate Grid";
        
        osg::ref_ptr<GeodeticGraticule> graticule = new GeodeticGraticule(graticuleOptions);
        map->addLayer(graticule);

        std::cout << "Added coordinate graticule" << std::endl;
    }

    return map;
}

/**
 * Configure viewer
 */
void configureViewer(osgViewer::Viewer& viewer, MapNode* mapNode)
{
    // Set Earth manipulator
    osg::ref_ptr<EarthManipulator> manip = new EarthManipulator();
    viewer.setCameraManipulator(manip);

    // Disable small feature culling
    viewer.getCamera()->setSmallFeatureCullingPixelSize(-1.0f);

    // Add statistics display
    viewer.addEventHandler(new osgViewer::StatsHandler());

    // Add window size adjustment handler
    viewer.addEventHandler(new osgViewer::WindowSizeHandler());

    // Add state set manipulator (for wireframe mode, etc.)
    viewer.addEventHandler(new osgGA::StateSetManipulator(viewer.getCamera()->getOrCreateStateSet()));

    // Add coordinate display handler
    viewer.addEventHandler(new CoordinateDisplayHandler(mapNode));

    // Set logarithmic depth buffer (improve far distance rendering)
    if (Registry::capabilities().supportsGLSL())
    {
        LogarithmicDepthBuffer logDepth;
        logDepth.install(viewer.getCamera());
        std::cout << "Enabled logarithmic depth buffer" << std::endl;
    }

    // Add auto clip plane handler
    viewer.addEventHandler(new AutoClipPlaneHandler());

    // Set initial viewpoint (Beijing)
    Viewpoint vp("Beijing", 116.4074, 39.9042, 0.0, 0.0, -45.0, 100000.0);
    manip->setViewpoint(vp);
    
    std::cout << "Set initial viewpoint to Beijing" << std::endl;
}

/**
 * Main function
 */
int main(int argc, char** argv)
{
    osg::ArgumentParser arguments(&argc, argv);

    // Display help information
    if (arguments.read("--help"))
    {
        std::cout << "osgEarth Custom Viewer" << std::endl;
        std::cout << "Features:" << std::endl;
        std::cout << "  - Display Google satellite imagery" << std::endl;
        std::cout << "  - Display OpenStreetMap (toggle with 'o')" << std::endl;
        std::cout << "  - Coordinate graticule display" << std::endl;
        std::cout << "  - Real-time coordinate display" << std::endl;
        std::cout << std::endl;
        std::cout << "Controls:" << std::endl;
        std::cout << "  Left mouse drag: Rotate view" << std::endl;
        std::cout << "  Right mouse drag: Zoom" << std::endl;
        std::cout << "  Middle mouse drag: Pan" << std::endl;
        std::cout << "  's': Show statistics" << std::endl;
        std::cout << "  'w': Wireframe mode" << std::endl;
        return 0;
    }

    // Initialize osgEarth
    osgEarth::initialize(arguments);
    
    // Configure network proxy
    configureNetworkProxy();

    std::cout << "Starting osgEarth custom viewer..." << std::endl;

    try
    {
        // Create map
        osg::ref_ptr<Map> map = createMap();
        if (!map.valid())
        {
            std::cerr << "Unable to create map" << std::endl;
            return -1;
        }

        // Create map node
        osg::ref_ptr<MapNode> mapNode = new MapNode(map);
        if (!mapNode.valid())
        {
            std::cerr << "Unable to create map node" << std::endl;
            return -1;
        }

        // Create viewer
        osgViewer::Viewer viewer(arguments);

        // Configure viewer
        configureViewer(viewer, mapNode);

        // Set scene data
        viewer.setSceneData(mapNode);

        // Set window size (not fullscreen)
        viewer.setUpViewInWindow(100, 100, 1024, 768);

        std::cout << "Map loaded successfully, starting rendering..." << std::endl;
        std::cout << std::endl << "Move mouse to view coordinate information" << std::endl;

        // Run viewer
        return viewer.run();
    }
    catch (const std::exception& e)
    {
        std::cerr << "Exception: " << e.what() << std::endl;
        return -1;
    }
    catch (...)
    {
        std::cerr << "Unknown exception" << std::endl;
        return -1;
    }
}
