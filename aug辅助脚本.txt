第一问：

对osgEarth项目进行重构，需要完成工作：

1. 在只需要加载 谷歌地图 xyz 瓦片和使用AWS Terrarium高程数据源 (PNG格式)的情况下，删除对GDAL库的依赖，对GDAL/OGR使用的矢量操作等，
使用geos库构建适配类替换；

2.在仅需少数必要WGS84与web墨卡托投影转换的前提下，删除对PROJ库的依赖，
改为使用geographiclib的适配器完成同样工作；

3.使用osgearth_viewer程序进行测试；

4.采用逐步重构，逐步编译测试的方法，自动化逐步完善以上工作

第二问：

需要继续工作。反馈信息：在最后一次的编译中，报告了错误，请排查修正并继续重构工作

第三问：
参考osgearth_viewer子项目，新建osgearth_myviewer，改造要求不通过读取.earth文件，
通过C++代码加载遥感图像xyz瓦片http://mt1.google.com/vt/lyrs=s&x={x}&y={y}&z={z} 和高程xyz瓦片https://s3.amazonaws.com/elevation-tiles-prod/terrarium/{z}/{x}/{y}.png，
并添加大气效果和经纬度显示。然后编译重构后的osgearth库和新建的osgearth_myviewer，
并发布到redist_desk目录，同时需要注意发布数据、配置、参数文件到正确目录