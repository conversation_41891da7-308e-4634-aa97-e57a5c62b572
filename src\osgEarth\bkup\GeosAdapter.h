/**
 * rocky c++
 * Copyright 2023 Pelican Mapping
 * MIT License
 */
#pragma once

#include <osgEarth/Common>
#include <osgEarth/GeoData>
#include <osgEarth/Feature>

#ifdef OSGEARTH_HAVE_GEOS
#include <geos_c.h>
#endif

#include <vector>
#include <memory>

namespace osgEarth
{
    /**
     * GEOS几何引擎适配器
     * 提供矢量几何处理功能，替代GDAL的几何功能
     */
    class OSGEARTH_EXPORT GeosAdapter
    {
    public:
        GeosAdapter();
        ~GeosAdapter();

        //! 初始化GEOS库
        bool initialize();

        //! 清理GEOS库
        void finalize();

        //! 创建点几何
        osg::ref_ptr<Geometry> createPoint(double x, double y, double z = 0.0);

        //! 创建线几何
        osg::ref_ptr<Geometry> createLineString(const std::vector<osg::Vec3d> &points);

        //! 创建多边形几何
        osg::ref_ptr<Geometry> createPolygon(const std::vector<osg::Vec3d> &exteriorRing,
                                             const std::vector<std::vector<osg::Vec3d>> &holes = {});

        //! 创建多点几何
        osg::ref_ptr<Geometry> createMultiPoint(const std::vector<osg::Vec3d> &points);

        //! 创建多线几何
        osg::ref_ptr<Geometry> createMultiLineString(const std::vector<std::vector<osg::Vec3d>> &lineStrings);

        //! 创建多多边形几何
        osg::ref_ptr<Geometry> createMultiPolygon(const std::vector<osg::ref_ptr<Geometry>> &polygons);

        //! 几何运算：缓冲区
        osg::ref_ptr<Geometry> buffer(const Geometry *geom, double distance);

        //! 几何运算：相交
        osg::ref_ptr<Geometry> intersection(const Geometry *geom1, const Geometry *geom2);

        //! 几何运算：并集
        osg::ref_ptr<Geometry> unionGeometry(const Geometry *geom1, const Geometry *geom2);

        //! 几何运算：差集
        osg::ref_ptr<Geometry> difference(const Geometry *geom1, const Geometry *geom2);

        //! 几何测试：是否相交
        bool intersects(const Geometry *geom1, const Geometry *geom2);

        //! 几何测试：是否包含
        bool contains(const Geometry *geom1, const Geometry *geom2);

        //! 几何测试：是否有效
        bool isValid(const Geometry *geom);

        //! 获取几何边界框
        GeoExtent getBounds(const Geometry *geom);

        //! 计算几何面积
        double getArea(const Geometry *geom);

        //! 计算几何长度
        double getLength(const Geometry *geom);

        //! 简化几何
        osg::ref_ptr<Geometry> simplify(const Geometry *geom, double tolerance);

        //! 从WKT创建几何
        osg::ref_ptr<Geometry> createFromWKT(const std::string &wkt);

        //! 转换几何为WKT
        std::string toWKT(const Geometry *geom);

        //! 获取全局实例
        static GeosAdapter &instance();

    private:
#ifdef OSGEARTH_HAVE_GEOS
        GEOSContextHandle_t _context;
        GEOSWKTReader *_wktReader;
        GEOSWKTWriter *_wktWriter;
#endif
        bool _initialized;

        // GEOS几何转换为osgEarth几何
        osg::ref_ptr<Geometry> convertFromGEOS(void *geosGeom);

        // osgEarth几何转换为GEOS几何
        void *convertToGEOS(const Geometry *geom);

        // 错误处理回调
        static void errorHandler(const char *fmt, ...);
        static void warningHandler(const char *fmt, ...);
    };

    /**
     * GEOS几何工厂
     * 提供便捷的几何创建方法
     */
    class OSGEARTH_EXPORT GeosGeometryFactory
    {
    public:
        //! 创建矩形多边形
        static osg::ref_ptr<Geometry> createRectangle(double minX, double minY, double maxX, double maxY);

        //! 创建圆形多边形
        static osg::ref_ptr<Geometry> createCircle(double centerX, double centerY, double radius, int segments = 32);

        //! 创建椭圆多边形
        static osg::ref_ptr<Geometry> createEllipse(double centerX, double centerY, double radiusX, double radiusY, int segments = 32);

        //! 创建正多边形
        static osg::ref_ptr<Geometry> createRegularPolygon(double centerX, double centerY, double radius, int sides);

        //! 从边界框创建多边形
        static osg::ref_ptr<Geometry> createFromExtent(const GeoExtent &extent);

        //! 创建网格线
        static std::vector<osg::ref_ptr<Geometry>> createGrid(const GeoExtent &extent, int rows, int cols);
    };
}
