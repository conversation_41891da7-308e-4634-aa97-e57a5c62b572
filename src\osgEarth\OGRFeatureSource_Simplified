/* -*-c++-*- */
/* osgEarth - Geospatial SDK for OpenSceneGraph
 * Copyright 2025 Pelican Mapping
 * http://osgearth.org
 *
 * Simplified OGRFeatureSource implementation without OGR dependencies
 */
#ifndef OSGEARTH_OGR_FEATURE_SOURCE_SIMPLIFIED_H
#define OSGEARTH_OGR_FEATURE_SOURCE_SIMPLIFIED_H 1

#include <osgEarth/Common>
#include <osgEarth/FeatureSource>
#include <osgEarth/URI>

namespace osgEarth
{
    /**
     * Simplified OGRFeatureSource for compatibility.
     * This is a stub implementation that provides the interface but no functionality.
     */
    class OSGEARTH_EXPORT OGRFeatureSource : public FeatureSource
    {
    public:
        class OSGEARTH_EXPORT Options : public FeatureSource::Options
        {
        public:
            META_LayerOptions(osgEarth, Options, FeatureSource::Options);
            OC_OPTION(URI, url);
            OC_OPTION(std::string, ogrDriver);
            OC_OPTION(std::string, geometryTypeOverride);
            OC_OPTION(bool, openWrite);
            OC_OPTION(bool, buildSpatialIndex);
            OC_OPTION(bool, forceRebuildSpatialIndex);
            virtual Config getConfig() const;
        private:
            void fromConfig(const Config& conf);
        };

    public:
        META_Layer(osgEarth, OGRFeatureSource, Options, FeatureSource, OGRFeatures);

        //! URL of the feature source
        void setURL(const URI& value);
        const URI& getURL() const;

        //! OGR driver name
        void setOGRDriver(const std::string& value);
        const std::string& getOGRDriver() const;

        //! Whether to build a spatial index
        void setBuildSpatialIndex(const bool& value);
        const bool& getBuildSpatialIndex() const;

    public: // FeatureSource

        virtual Status openImplementation() override;
        virtual Status closeImplementation() override;
        virtual FeatureCursor* createFeatureCursorImplementation(const Query& query, ProgressCallback* progress) const override;
        virtual bool deleteFeature(FeatureID fid) override;
        virtual int getFeatureCount() const override;
        virtual bool supportsGetFeature() const override;
        virtual Feature* getFeature(FeatureID fid) override;
        virtual bool isWritable() const override;
        virtual const FeatureSchema& getSchema() const override;
        virtual bool insertFeature(Feature* feature) override;
        virtual Geometry::Type getGeometryType() const override;
        virtual const Status& create(const FeatureProfile* profile, const FeatureSchema& schema, const Geometry::Type& geometryType, const osgDB::Options* readOptions) override;
        virtual void buildSpatialIndex() override;
        virtual void dirty() override;

    protected:
        virtual void init() override;
        virtual ~OGRFeatureSource();

    private:
        mutable FeatureSchema _schema;
        mutable Status _createStatus;
    };

} // namespace osgEarth

#endif // OSGEARTH_OGR_FEATURE_SOURCE_SIMPLIFIED_H
